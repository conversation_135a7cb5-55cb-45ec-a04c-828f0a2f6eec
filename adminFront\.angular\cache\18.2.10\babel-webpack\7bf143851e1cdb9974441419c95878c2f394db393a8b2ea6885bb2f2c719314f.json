{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { trigger, style, transition, animate } from '@angular/animations';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_55_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 47);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_55_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_55_button_37_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearAllFilters());\n    });\n    i0.ɵɵelement(1, \"i\", 64);\n    i0.ɵɵtext(2, \"\\u6E05\\u9664\\u7BE9\\u9078 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49)(2, \"div\", 22)(3, \"nb-form-field\", 32);\n    i0.ɵɵelement(4, \"nb-icon\", 50);\n    i0.ɵɵelementStart(5, \"input\", 51);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_55_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_55_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"nb-select\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_55_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_55_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 29);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 53);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 54);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 55);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 56);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 57);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 27)(21, \"nb-select\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_55_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_55_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 29);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_55_nb_option_24_Template, 2, 2, \"nb-option\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 59)(26, \"nb-select\", 60);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_55_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_55_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 47);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 47);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 47);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 47);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 38)(36, \"div\", 61);\n    i0.ɵɵtemplate(37, SettingTimePeriodComponent_div_55_button_37_Template, 3, 0, \"button\", 62);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.hasActiveFilters());\n  }\n}\nfunction SettingTimePeriodComponent_div_56_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 107);\n    i0.ɵɵelement(1, \"i\", 108);\n    i0.ɵɵtext(2, \" \\u5DF2\\u9078 \");\n    i0.ɵɵelementStart(3, \"strong\", 109);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7B46 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_56_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"button\", 111);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_32_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(2, \"i\", 112);\n    i0.ɵɵtext(3, \"\\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵelementStart(4, \"span\", 113);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_32_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearSelection());\n    });\n    i0.ɵɵelement(7, \"i\", 64);\n    i0.ɵɵtext(8, \"\\u6E05\\u9664\\u9078\\u64C7 \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r4.selectedHouses.length);\n  }\n}\nfunction SettingTimePeriodComponent_div_56_tr_74_small_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"small\", 131);\n    i0.ɵɵtext(1, \"\\u7121\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_56_tr_74_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 132);\n    i0.ɵɵelement(1, \"i\", 133);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r11.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_56_tr_74_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 134);\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_56_tr_74_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 132);\n    i0.ɵɵelement(1, \"i\", 135);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(3, 1, house_r11.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_56_tr_74_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 134);\n    i0.ɵɵelement(1, \"i\", 79);\n    i0.ɵɵtext(2, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_56_tr_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 115)(2, \"nb-checkbox\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_tr_74_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r11.selected, $event) || (house_r11.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_tr_74_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\")(4, \"div\", 117)(5, \"span\", 118);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SettingTimePeriodComponent_div_56_tr_74_small_7_Template, 2, 0, \"small\", 119);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\")(9, \"span\", 120);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 115)(12, \"span\", 121);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"td\")(15, \"div\", 122);\n    i0.ɵɵtemplate(16, SettingTimePeriodComponent_div_56_tr_74_span_16_Template, 4, 4, \"span\", 123)(17, SettingTimePeriodComponent_div_56_tr_74_span_17_Template, 3, 0, \"span\", 124);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 122);\n    i0.ɵɵtemplate(20, SettingTimePeriodComponent_div_56_tr_74_span_20_Template, 4, 4, \"span\", 123)(21, SettingTimePeriodComponent_div_56_tr_74_span_21_Template, 3, 0, \"span\", 124);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"td\", 115)(23, \"div\", 125)(24, \"span\", 126);\n    i0.ɵɵelement(25, \"i\", 127);\n    i0.ɵɵelementStart(26, \"span\", 128);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"td\", 115)(29, \"div\", 39)(30, \"button\", 129);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_tr_74_Template_button_click_30_listener() {\n      const house_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r12 = i0.ɵɵreference(64);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r12, house_r11));\n    });\n    i0.ɵɵelement(31, \"i\", 130);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const house_r11 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r11.selected)(\"table-row-disabled\", !house_r11.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r11.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(house_r11.CHouseHold);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CHouseId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(house_r11.CBuildingName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", house_r11.CFloor, \"F\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeStartDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", house_r11.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r11.CChangeEndDate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r11));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getStatusIcon(house_r11));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r4.getStatusText(house_r11));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !house_r11.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_56_div_75_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 141)(1, \"button\", 156);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_75_li_21_Template_button_click_1_listener() {\n      const page_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r15));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r15 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r15);\n  }\n}\nfunction SettingTimePeriodComponent_div_56_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"div\", 67)(2, \"div\", 137)(3, \"span\", 91);\n    i0.ɵɵtext(4, \" \\u7B2C \");\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" \\u9801\\uFF0C\\u5171 \");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" \\u9801 \");\n    i0.ɵɵelementStart(11, \"span\", 138);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"nav\", 139)(14, \"ul\", 140)(15, \"li\", 141)(16, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_75_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵelement(17, \"i\", 143);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 141)(19, \"button\", 144);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_75_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵelement(20, \"i\", 145);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(21, SettingTimePeriodComponent_div_56_div_75_li_21_Template, 3, 3, \"li\", 146);\n    i0.ɵɵelementStart(22, \"li\", 141)(23, \"button\", 147);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_75_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵelement(24, \"i\", 148);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"li\", 141)(26, \"button\", 149);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_75_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵelement(27, \"i\", 150);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 151)(29, \"div\", 152)(30, \"input\", 153);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_div_75_Template_input_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.jumpToPage, $event) || (ctx_r4.jumpToPage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keyup.enter\", function SettingTimePeriodComponent_div_56_div_75_Template_input_keyup_enter_30_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"button\", 154);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_div_75_Template_button_click_31_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.jumpToPageAction());\n    });\n    i0.ɵɵelement(32, \"i\", 155);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r4.currentPage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r4.totalPages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate3(\" (\\u986F\\u793A\\u7B2C \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" \\u7B46\\uFF0C \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46) \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.jumpToPage);\n    i0.ɵɵproperty(\"min\", 1)(\"max\", ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66)(2, \"div\", 67)(3, \"div\", 68)(4, \"span\", 69);\n    i0.ɵɵelement(5, \"i\", 70);\n    i0.ɵɵtext(6, \" \\u5171 \");\n    i0.ɵɵelementStart(7, \"strong\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_56_span_10_Template, 6, 1, \"span\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 72)(12, \"div\", 73)(13, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"active\"));\n    });\n    i0.ɵɵelement(14, \"i\", 75);\n    i0.ɵɵtext(15, \"\\u9032\\u884C\\u4E2D \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"pending\"));\n    });\n    i0.ɵɵelement(17, \"i\", 76);\n    i0.ɵɵtext(18, \"\\u5F85\\u958B\\u653E \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 74);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"expired\"));\n    });\n    i0.ɵɵelement(20, \"i\", 77);\n    i0.ɵɵtext(21, \"\\u5DF2\\u904E\\u671F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 78);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setQuickFilter(\"not-set\"));\n    });\n    i0.ɵɵelement(23, \"i\", 79);\n    i0.ɵɵtext(24, \"\\u672A\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(25, \"div\", 80)(26, \"div\", 67)(27, \"div\", 81)(28, \"div\", 82)(29, \"nb-checkbox\", 83);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_checkbox_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_checkbox_ngModelChange_29_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementStart(30, \"span\", 84);\n    i0.ɵɵtext(31, \"\\u5168\\u9078\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, SettingTimePeriodComponent_div_56_div_32_Template, 9, 1, \"div\", 85);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 86)(34, \"div\", 87)(35, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.exportData());\n    });\n    i0.ɵɵelement(36, \"i\", 89);\n    i0.ɵɵtext(37, \"\\u532F\\u51FA \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 90)(39, \"small\", 91);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(41, \"div\", 92)(42, \"table\", 93)(43, \"thead\", 94)(44, \"tr\")(45, \"th\", 95)(46, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_checkbox_ngModelChange_46_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_56_Template_nb_checkbox_ngModelChange_46_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"th\", 97);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_th_click_47_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵelementStart(48, \"div\", 98);\n    i0.ɵɵtext(49, \" \\u6236\\u578B \");\n    i0.ɵɵelement(50, \"i\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"th\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_th_click_51_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵelementStart(52, \"div\", 98);\n    i0.ɵɵtext(53, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(54, \"i\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"th\", 101);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_th_click_55_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵelementStart(56, \"div\", 98);\n    i0.ɵɵtext(57, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(58, \"i\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"th\", 102);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_th_click_59_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵelementStart(60, \"div\", 98);\n    i0.ɵɵtext(61, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(62, \"i\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"th\", 102);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_56_Template_th_click_63_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵelementStart(64, \"div\", 98);\n    i0.ɵɵtext(65, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(66, \"i\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"th\", 103)(68, \"div\", 98);\n    i0.ɵɵtext(69, \" \\u72C0\\u614B \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"th\", 104)(71, \"div\", 98);\n    i0.ɵɵtext(72, \" \\u64CD\\u4F5C \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(73, \"tbody\");\n    i0.ɵɵtemplate(74, SettingTimePeriodComponent_div_56_tr_74_Template, 32, 20, \"tr\", 105);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(75, SettingTimePeriodComponent_div_56_div_75_Template, 33, 21, \"div\", 106);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r4.filteredHouses.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.filterOptions.statusFilter === \"active\")(\"btn-outline-primary\", ctx_r4.filterOptions.statusFilter !== \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-warning\", ctx_r4.filterOptions.statusFilter === \"pending\")(\"btn-outline-warning\", ctx_r4.filterOptions.statusFilter !== \"pending\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-danger\", ctx_r4.filterOptions.statusFilter === \"expired\")(\"btn-outline-danger\", ctx_r4.filterOptions.statusFilter !== \"expired\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-not-set-active\", ctx_r4.filterOptions.statusFilter === \"not-set\")(\"btn-not-set-outline\", ctx_r4.filterOptions.statusFilter !== \"not-set\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.filteredHouses.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 91);\n    i0.ɵɵelement(4, \"i\", 158);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 91)(4, \"div\", 159)(5, \"span\", 160);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 161);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 109);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \\u5DF2\\u9078\\u64C7 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225 \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", ctx_r4.selectedBuildingForBatch.name, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_28_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 180);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" \", house_r17.CHouseHold, \" (\", house_r17.CBuildingName, \"-\", house_r17.CFloor, \"F) \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_28_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" ...\\u7B49 \", ctx_r4.selectedHouses.length - 10, \" \\u500B \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 176)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 177);\n    i0.ɵɵtemplate(4, SettingTimePeriodComponent_ng_template_59_div_28_span_4_Template, 2, 3, \"span\", 178)(5, SettingTimePeriodComponent_ng_template_59_div_28_span_5_Template, 2, 1, \"span\", 179);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u5C07\\u5957\\u7528\\u5230\\u5DF2\\u9078\\u64C7\\u7684 \", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225\\uFF1A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedHouses.slice(0, 10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 10);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 116);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r22 = i0.ɵɵrestoreView(_r21).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r22.selected, $event) || (house_r22.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r22 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r22.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r22.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r22.CHouseHold, \" (\", house_r22.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 185);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 186);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r20.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 183)(1, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r20.selected, $event) || (floor_r20.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r20 = i0.ɵɵrestoreView(_r19).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r20));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_Template, 2, 1, \"div\", 184);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r20 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r20.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r20.floorNumber, \"F (\", floor_r20.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r20.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 161);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_Template, 4, 4, \"div\", 182);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"nb-checkbox\", 96);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_div_29_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_59_div_29_div_3_Template, 2, 1, \"div\", 181);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u5168\\u90E8\\u6236\\u5225 (\", ctx_r4.flattenedHouses.length, \" \\u500B) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r4.selectedHouses.length, \" \\u500B\\u6236\\u5225)\");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 162)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_59_span_3_Template, 2, 1, \"span\", 163)(4, SettingTimePeriodComponent_ng_template_59_span_4_Template, 2, 1, \"span\", 164);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"nb-card-body\")(6, \"div\", 165)(7, \"label\");\n    i0.ɵɵtext(8, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(9, \"span\", 166);\n    i0.ɵɵtext(10, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 167)(12, \"nb-form-field\", 168);\n    i0.ɵɵelement(13, \"nb-icon\", 33);\n    i0.ɵɵelementStart(14, \"input\", 169);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 35, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 170);\n    i0.ɵɵtext(18, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\", 168);\n    i0.ɵɵelement(20, \"nb-icon\", 33);\n    i0.ɵɵelementStart(21, \"input\", 169);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 35, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 165)(25, \"label\");\n    i0.ɵɵtext(26, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 171);\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_59_div_28_Template, 6, 3, \"div\", 172)(29, SettingTimePeriodComponent_ng_template_59_div_29_Template, 4, 3, \"div\", 164);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"nb-card-footer\", 173)(31, \"button\", 174);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_59_Template_button_click_31_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r23));\n    });\n    i0.ɵɵtext(32, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 175);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_59_Template_button_click_33_listener() {\n      const ref_r23 = i0.ɵɵrestoreView(_r16).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r23));\n    });\n    i0.ɵɵtext(34, \" \\u6279\\u6B21\\u8A2D\\u5B9A \");\n    i0.ɵɵtemplate(35, SettingTimePeriodComponent_ng_template_59_span_35_Template, 2, 1, \"span\", 164);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r24 = i0.ɵɵreference(16);\n    const batchEndDate_r25 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedBuildingForBatch && ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r24);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 187);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 188);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_63_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 187)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 189);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 190)(7, \"div\", 191)(8, \"label\", 192);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 166);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 193);\n    i0.ɵɵelement(13, \"nb-icon\", 33);\n    i0.ɵɵelementStart(14, \"input\", 194);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 35, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 195);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 33);\n    i0.ɵɵelementStart(21, \"input\", 196);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 35, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 188)(25, \"button\", 197);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_63_Template_button_click_25_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r27));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 198);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_63_Template_button_click_27_listener() {\n      const ref_r27 = i0.ɵɵrestoreView(_r26).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r27));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r28 = i0.ɵɵreference(16);\n    const changeEndDate_r29 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r28);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r29);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 表格視圖相關屬性\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 50;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    // 新增的UI控制屬性\n    this.showAdvancedFilters = false;\n    this.jumpToPage = 1;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  // 新增的UI控制方法\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  hasActiveFilters() {\n    return !!(this.filterOptions.searchKeyword || this.filterOptions.statusFilter || this.filterOptions.floorFilter);\n  }\n  getActiveFiltersCount() {\n    let count = 0;\n    if (this.filterOptions.searchKeyword) count++;\n    if (this.filterOptions.statusFilter) count++;\n    if (this.filterOptions.floorFilter) count++;\n    return count;\n  }\n  resetFilters() {\n    this.searchQuery = {\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.selectedBuilding = '';\n    this.clearAllFilters();\n  }\n  clearAllFilters() {\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    this.onSearch();\n  }\n  setQuickFilter(status) {\n    if (this.filterOptions.statusFilter === status) {\n      this.filterOptions.statusFilter = '';\n    } else {\n      this.filterOptions.statusFilter = status;\n    }\n    this.onSearch();\n  }\n  clearSelection() {\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.flattenedHouses.forEach(house => house.selected = false);\n  }\n  getStatusIcon(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return 'fas fa-play-circle';\n      case 'pending':\n        return 'fas fa-clock';\n      case 'expired':\n        return 'fas fa-times-circle';\n      case 'not-set':\n        return 'fas fa-exclamation-triangle';\n      case 'disabled':\n        return 'fas fa-ban';\n      default:\n        return 'fas fa-exclamation-triangle';\n    }\n  }\n  jumpToPageAction() {\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\n      this.goToPage(this.jumpToPage);\n    }\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  // 新增：建案變更處理\n  onBuildCaseChange() {\n    // 重置所有相關狀態\n    this.resetAllStates();\n    // 執行查詢\n    this.getHouseChangeDate();\n  }\n  // 新增：重置所有狀態\n  resetAllStates() {\n    // 重置數據\n    this.houseChangeDates = [];\n    this.convertedHouseArray = [];\n    this.buildingGroups = [];\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    // 重置篩選條件\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 重置選擇狀態\n    this.selectAll = false;\n    this.selectedBuilding = '';\n    // 重置分頁\n    this.currentPage = 1;\n    this.totalPages = 1;\n    // 重置可用選項\n    this.buildingOptions = [];\n    this.availableFloors = [];\n    // 重置排序\n    this.sortField = '';\n    this.sortDirection = 'asc';\n  }\n  getHouseChangeDate() {\n    // 如果沒有選擇建案，直接返回\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\n      this.loading = false;\n      return;\n    }\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 修改：棟別選擇變更處理\n  onBuildingChange() {\n    // 重置選擇狀態\n    this.selectedHouses.forEach(house => house.selected = false);\n    this.selectedHouses = [];\n    this.selectAll = false;\n    // 重置分頁到第一頁\n    this.currentPage = 1;\n    // 重置樓層篩選\n    this.filterOptions.floorFilter = '';\n    // 更新可用樓層\n    this.updateAvailableFloors();\n    // 設定棟別篩選並執行搜尋\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n    this.onSearch();\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 修復：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'pending':\n        return status === 'pending';\n      case 'expired':\n        return status === 'expired';\n      case 'not-set':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n      // 全部狀態\n    }\n  }\n  // 修復：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    // 檢查是否有設定開放時段\n    if (!house.CChangeStartDate || !house.CChangeEndDate || house.CChangeStartDate === '' || house.CChangeEndDate === '') {\n      return 'not-set';\n    }\n    try {\n      // 處理日期字串，支援多種格式\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      // 解析開始日期\n      let startDate;\n      if (house.CChangeStartDate.includes('T')) {\n        startDate = new Date(house.CChangeStartDate);\n      } else {\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\n      }\n      // 解析結束日期\n      let endDate;\n      if (house.CChangeEndDate.includes('T')) {\n        endDate = new Date(house.CChangeEndDate);\n      } else {\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\n      }\n      // 檢查日期有效性\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\n        console.warn('Invalid date format:', {\n          start: house.CChangeStartDate,\n          end: house.CChangeEndDate,\n          houseId: house.CHouseId\n        });\n        return 'not-set';\n      }\n      // 轉換為日期比較（不含時間）\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\n      // 判斷狀態\n      if (today < startDateOnly) {\n        return 'pending'; // 待開放\n      } else if (today >= startDateOnly && today <= endDateOnly) {\n        return 'active'; // 進行中\n      } else {\n        return 'expired'; // 已過期\n      }\n    } catch (error) {\n      console.error('Error parsing dates:', error, house);\n      return 'not-set';\n    }\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 修改：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\n    const hasSelectedHouses = this.selectedHouses.length > 0;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !hasSelectedHouses && !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 修改：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.flattenedHouses.forEach(house => {\n        if (house.CHouseId) {\n          housesToUpdate.push({\n            CHouseId: house.CHouseId,\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n          });\n        }\n      });\n    } else {\n      // 使用已選擇的戶別\n      if (this.selectedHouses.length > 0) {\n        this.selectedHouses.forEach(house => {\n          if (house.CHouseId) {\n            housesToUpdate.push({\n              CHouseId: house.CHouseId,\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n            });\n          }\n        });\n      } else if (this.selectedBuildingForBatch) {\n        // 如果沒有選擇的戶別，使用舊的邏輯\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        // 清除選擇狀態\n        this.selectedHouses.forEach(house => house.selected = false);\n        this.selectedHouses = [];\n        this.selectAll = false;\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n    // 調試：輸出狀態統計\n    this.debugStatusCounts();\n  }\n  // 調試：輸出狀態統計\n  debugStatusCounts() {\n    const statusCounts = {\n      active: 0,\n      pending: 0,\n      expired: 0,\n      'not-set': 0,\n      disabled: 0\n    };\n    this.flattenedHouses.forEach(house => {\n      const status = this.getHouseStatus(house);\n      if (statusCounts.hasOwnProperty(status)) {\n        statusCounts[status]++;\n      }\n    });\n    console.log('狀態統計:', statusCounts);\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\n  }\n  // 修改：搜尋和篩選\n  onSearch() {\n    // 記錄篩選前的已選擇項目\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\n    this.selectedHouses = this.filteredHouses.filter(house => previouslySelectedIds.includes(house.CHouseId));\n    // 更新扁平化資料中的選擇狀態\n    this.flattenedHouses.forEach(house => {\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\n    });\n    // 更新全選狀態\n    this.updateSelectAllState();\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新全選狀態\n  updateSelectAllState() {\n    if (this.paginatedHouses.length === 0) {\n      this.selectAll = false;\n    } else {\n      this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n    }\n  }\n  // 修改：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n    // 更新全選狀態\n    this.updateSelectAllState();\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 65,\n      vars: 14,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"page-header-optimized\"], [1, \"page-title-section\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"title-group\"], [1, \"page-title\", \"mb-1\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"page-subtitle\", \"text-muted\", \"mb-0\"], [1, \"help-section\"], [\"type\", \"button\", \"data-bs-toggle\", \"tooltip\", \"title\", \"\\u8A2D\\u5B9A\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\uFF0C\\u63A7\\u5236\\u5BA2\\u6236\\u9078\\u6A23\\u6B0A\\u9650\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\"], [1, \"fas\", \"fa-question-circle\"], [1, \"compact-filters\"], [1, \"row\", \"g-3\", \"align-items-end\"], [1, \"col-lg-3\", \"col-md-4\"], [1, \"form-label\", \"small\", \"fw-medium\"], [1, \"text-danger\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-2\", \"col-md-3\"], [\"placeholder\", \"\\u5168\\u90E8\\u68DF\\u5225\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"col-lg-4\", \"col-md-5\"], [1, \"date-range-group\"], [\"size\", \"small\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u958B\\u59CB\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"date-separator\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u7D50\\u675F\\u65E5\\u671F\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-lg-3\", \"col-md-12\"], [1, \"action-buttons\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-search\", \"me-1\"], [\"title\", \"\\u91CD\\u7F6E\\u7BE9\\u9078\\u689D\\u4EF6\", 1, \"btn\", \"btn-outline-secondary\", \"ms-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-undo\"], [\"class\", \"advanced-filters-panel\", 4, \"ngIf\"], [\"class\", \"table-view-enhanced mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"advanced-filters-panel\"], [1, \"row\", \"g-3\", \"align-items-center\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-lg-2\", \"col-md-2\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", \"size\", \"small\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"filter-actions\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"table-view-enhanced\", \"mt-4\"], [1, \"data-summary-bar\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"summary-info\"], [1, \"total-count\"], [1, \"fas\", \"fa-database\", \"me-1\"], [\"class\", \"selected-count\", 4, \"ngIf\"], [1, \"quick-filters\"], [\"role\", \"group\", 1, \"btn-group\", \"btn-group-sm\"], [\"type\", \"button\", 1, \"btn\", 3, \"click\"], [1, \"fas\", \"fa-play-circle\", \"me-1\"], [1, \"fas\", \"fa-clock\", \"me-1\"], [1, \"fas\", \"fa-times-circle\", \"me-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-not-set\", 3, \"click\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [1, \"enhanced-toolbar\"], [1, \"batch-operations\"], [1, \"selection-controls\"], [1, \"select-all-checkbox\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fw-medium\"], [\"class\", \"batch-actions ms-3\", 4, \"ngIf\"], [1, \"table-controls\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\"], [\"title\", \"\\u532F\\u51FA\\u8CC7\\u6599\", 1, \"btn\", \"btn-outline-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"me-1\"], [1, \"pagination-summary\"], [1, \"text-muted\"], [1, \"enhanced-table-container\"], [1, \"table\", \"table-hover\", \"enhanced-table\"], [1, \"enhanced-table-header\"], [\"width\", \"50\", 1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [1, \"header-content\"], [1, \"fas\", \"fa-sort\", \"sort-icon\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [\"width\", \"80\", 1, \"sortable\", \"text-center\", 3, \"click\"], [\"width\", \"140\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"text-center\"], [\"width\", \"100\", 1, \"text-center\"], [3, \"table-row-selected\", \"table-row-disabled\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"enhanced-pagination-container\", 4, \"ngIf\"], [1, \"selected-count\"], [1, \"fas\", \"fa-check-square\", \"me-1\", \"text-primary\"], [1, \"text-primary\"], [1, \"batch-actions\", \"ms-3\"], [\"title\", \"\\u6279\\u6B21\\u8A2D\\u5B9A\\u9078\\u4E2D\\u7684\\u6236\\u5225\\u958B\\u653E\\u6642\\u6BB5\", 1, \"btn\", \"btn-warning\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [1, \"badge\", \"bg-light\", \"text-dark\", \"ms-1\"], [\"title\", \"\\u6E05\\u9664\\u9078\\u64C7\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", \"ms-2\", 3, \"click\"], [1, \"text-center\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [1, \"house-info\"], [1, \"house-name\", \"fw-medium\"], [\"class\", \"text-muted d-block\", 4, \"ngIf\"], [1, \"building-name\"], [1, \"floor-badge\"], [1, \"date-info\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"not-set-text\", 4, \"ngIf\"], [1, \"status-display\"], [1, \"enhanced-status-badge\"], [1, \"status-icon\"], [1, \"status-text\"], [\"title\", \"\\u7DE8\\u8F2F\\u6642\\u6BB5\\u8A2D\\u5B9A\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"text-muted\", \"d-block\"], [1, \"date-display\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-success\"], [1, \"not-set-text\"], [1, \"fas\", \"fa-calendar\", \"me-1\", \"text-danger\"], [1, \"enhanced-pagination-container\"], [1, \"pagination-info-detailed\"], [1, \"ms-2\"], [1, \"pagination-nav\"], [1, \"pagination\", \"pagination-sm\", \"mb-0\"], [1, \"page-item\"], [\"title\", \"\\u7B2C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-left\"], [\"title\", \"\\u4E0A\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-left\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"\\u4E0B\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-right\"], [\"title\", \"\\u6700\\u5F8C\\u4E00\\u9801\", 1, \"page-link\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-angle-double-right\"], [1, \"quick-jump\"], [1, \"input-group\", \"input-group-sm\", 2, \"width\", \"120px\"], [\"type\", \"number\", \"placeholder\", \"\\u9801\\u78BC\", 1, \"form-control\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\", \"min\", \"max\"], [\"type\", \"button\", \"title\", \"\\u8DF3\\u8F49\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fas\", \"fa-arrow-right\"], [1, \"page-link\", 3, \"click\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"alert\", \"alert-info\"], [1, \"selected-houses-preview\"], [\"class\", \"badge badge-primary mr-1 mb-1\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"badge\", \"badge-primary\", \"mr-1\", \"mb-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 10)(5, \"div\", 11)(6, \"div\", 12)(7, \"div\", 13)(8, \"h5\", 14);\n          i0.ɵɵelement(9, \"i\", 15);\n          i0.ɵɵtext(10, \"\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 16);\n          i0.ɵɵtext(12, \"\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u9593\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 17)(14, \"button\", 18);\n          i0.ɵɵelement(15, \"i\", 19);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(16, \"div\", 20)(17, \"div\", 21)(18, \"div\", 22)(19, \"label\", 23);\n          i0.ɵɵtext(20, \"\\u5EFA\\u6848 \");\n          i0.ɵɵelementStart(21, \"span\", 24);\n          i0.ɵɵtext(22, \"*\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"nb-select\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildCaseChange());\n          });\n          i0.ɵɵtemplate(24, SettingTimePeriodComponent_nb_option_24_Template, 2, 2, \"nb-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 27)(26, \"label\", 23);\n          i0.ɵɵtext(27, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"nb-select\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_28_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(29, \"nb-option\", 29);\n          i0.ɵɵtext(30, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, SettingTimePeriodComponent_nb_option_31_Template, 2, 2, \"nb-option\", 26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 30)(33, \"label\", 23);\n          i0.ɵɵtext(34, \"\\u958B\\u653E\\u65E5\\u671F\\u7BC4\\u570D\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 31)(36, \"nb-form-field\", 32);\n          i0.ɵɵelement(37, \"nb-icon\", 33);\n          i0.ɵɵelementStart(38, \"input\", 34);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_38_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(39, \"nb-datepicker\", 35, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"span\", 36);\n          i0.ɵɵtext(42, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"nb-form-field\", 32);\n          i0.ɵɵelement(44, \"nb-icon\", 33);\n          i0.ɵɵelementStart(45, \"input\", 37);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_45_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"nb-datepicker\", 35, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 38)(49, \"div\", 39)(50, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_50_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵelement(51, \"i\", 41);\n          i0.ɵɵtext(52, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"button\", 42);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_53_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.resetFilters());\n          });\n          i0.ɵɵelement(54, \"i\", 43);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(55, SettingTimePeriodComponent_div_55_Template, 38, 10, \"div\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(56, SettingTimePeriodComponent_div_56_Template, 76, 48, \"div\", 45)(57, SettingTimePeriodComponent_div_57_Template, 7, 0, \"div\", 46)(58, SettingTimePeriodComponent_div_58_Template, 9, 0, \"div\", 46);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(59, SettingTimePeriodComponent_ng_template_59_Template, 36, 9, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(61, SettingTimePeriodComponent_ng_template_61_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(63, SettingTimePeriodComponent_ng_template_63_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r30 = i0.ɵɵreference(40);\n          const EndDate_r31 = i0.ɵɵreference(47);\n          i0.ɵɵadvance(23);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r30);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r31);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.NumberValueAccessor, i9.NgControlStatus, i9.MinValidator, i9.MaxValidator, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\"@charset \\\"UTF-8\\\";\\n\\n\\n\\n\\n\\n.page-header-optimized[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  border-radius: 0.75rem;\\n  box-shadow: 0 2px 8px rgba(184, 166, 118, 0.1);\\n  margin-bottom: 1.5rem;\\n  overflow: hidden;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%] {\\n  padding: 1.25rem 1.5rem 1rem;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .title-group[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n  font-size: 1.25rem;\\n  margin-bottom: 0.25rem;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .title-group[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  font-size: 1.1rem;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .title-group[_ngcontent-%COMP%]   .page-subtitle[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n  line-height: 1.4;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .help-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  transition: all 0.3s ease;\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .help-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: #B8A676;\\n  transform: scale(1.05);\\n}\\n.page-header-optimized[_ngcontent-%COMP%]   .page-title-section[_ngcontent-%COMP%]   .help-section[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #B8A676;\\n}\\n\\n.compact-filters[_ngcontent-%COMP%] {\\n  padding: 1.25rem 1.5rem;\\n  background: linear-gradient(to bottom, #fafafa 0%, #f8f9fa 100%);\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.15);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.375rem;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .date-range-group[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-weight: 500;\\n  padding: 0 0.25rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n  justify-content: flex-end;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.3s ease;\\n  padding: 0.5rem 1rem;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #A69660 0%, #95854A 100%);\\n  border-color: #A69660;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.3);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  border-color: #6c757d;\\n  color: #6c757d;\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background-color: #6c757d;\\n  border-color: #6c757d;\\n  color: white;\\n  transform: translateY(-1px);\\n}\\n.compact-filters[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n\\n.advanced-filters-toggle[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1.5rem 0;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  text-decoration: none;\\n  font-weight: 500;\\n  font-size: 0.875rem;\\n  transition: all 0.3s ease;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  color: #a59056;\\n  text-decoration: none;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease;\\n}\\n.advanced-filters-toggle[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.25rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n  animation: _ngcontent-%COMP%_slideDown 0.3s ease-out;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  align-items: center;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .filter-actions[_ngcontent-%COMP%]   .btn-outline-danger[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideDown {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.advanced-filters[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem 1.5rem;\\n  background-color: #ffffff;\\n  border-top: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%]   .text-primary[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #B8A676;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%], \\n.advanced-filters[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-select.ng-touched.ng-valid[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.5);\\n}\\n.advanced-filters[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 1rem 1.25rem;\\n  border-radius: 0.5rem 0.5rem 0 0;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], \\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .summary-info[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-secondary[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-warning[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-danger[_ngcontent-%COMP%], .table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);\\n  border-color: #ffc107;\\n  color: #212529;\\n  font-weight: 600;\\n  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-active[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #856404;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border: 2px solid #ffc107;\\n  color: #856404;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  border-color: #e0a800;\\n  color: #856404;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(255, 193, 7, 0.2);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .data-summary-bar[_ngcontent-%COMP%]   .quick-filters[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.btn-not-set.btn-not-set-outline[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #e0a800;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1rem 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-bottom: none;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .select-all-checkbox[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n  border-color: #D4B96A;\\n  color: #5a4a2a;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #C4A85A 0%, #B4984A 100%);\\n  border-color: #C4A85A;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn.btn-warning[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  color: #B8A676;\\n  font-weight: 600;\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .batch-operations[_ngcontent-%COMP%]   .selection-controls[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .btn-outline-success[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(40, 167, 69, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-toolbar[_ngcontent-%COMP%]   .table-controls[_ngcontent-%COMP%]   .pagination-summary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n  margin-left: 1rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%] {\\n  border: 1px solid rgba(184, 166, 118, 0.2);\\n  border-radius: 0 0 0.5rem 0.5rem;\\n  overflow: hidden;\\n  box-shadow: 0 4px 8px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  font-size: 0.875rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F8F6F0 0%, #F0EDE5 100%);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 1rem 0.75rem;\\n  border-bottom: 2px solid rgba(184, 166, 118, 0.2);\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 0.4;\\n  color: #6c757d;\\n  transition: opacity 0.3s ease;\\n  margin-left: 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   .enhanced-table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   .sort-icon[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  color: #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.05);\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%] {\\n  background-color: rgba(184, 166, 118, 0.15);\\n  border-left: 3px solid #B8A676;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  background-color: #f8f9fa;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.75rem;\\n  vertical-align: middle;\\n  border-bottom: 1px solid rgba(184, 166, 118, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   .house-name[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-size: 0.9rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .house-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .building-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .floor-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\\n  color: #495057;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.85rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%] {\\n  color: #856404;\\n  font-weight: 500;\\n  font-size: 0.85rem;\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #ffc107;\\n  display: inline-flex;\\n  align-items: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .not-set-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n  font-size: 0.75rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 1rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  margin-right: 0.375rem;\\n  font-size: 0.7rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  white-space: nowrap;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .status-display[_ngcontent-%COMP%]   .enhanced-status-badge[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: scale(1.1);\\n  box-shadow: 0 3px 6px rgba(0, 123, 255, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-table-container[_ngcontent-%COMP%]   .enhanced-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E8F5E8 0%, #D4EDDA 100%);\\n  color: #2D5A2D;\\n  border-color: rgba(45, 90, 45, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-active[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFF8E1 0%, #FFF3CD 100%);\\n  color: #8B6914;\\n  border-color: rgba(139, 105, 20, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-pending[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FFEBEE 0%, #F8D7DA 100%);\\n  color: #8B2635;\\n  border-color: rgba(139, 38, 53, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-expired[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);\\n  color: #856404;\\n  border-color: rgba(255, 193, 7, 0.4);\\n  font-weight: 600;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-not-set[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #ffc107;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #FAFAFA 0%, #F8F9FA 100%);\\n  color: #8A8A8A;\\n  border-color: rgba(138, 138, 138, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-status-badge.status-disabled[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);\\n  padding: 1.25rem;\\n  border: 1px solid rgba(184, 166, 118, 0.15);\\n  border-top: none;\\n  border-radius: 0 0 0.5rem 0.5rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-info-detailed[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #2c3e50;\\n  font-weight: 600;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%] {\\n  margin: 0 0.125rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #495057;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.3s ease;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-weight: 500;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(184, 166, 118, 0.2);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .pagination-nav[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n  cursor: not-allowed;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  font-size: 0.8rem;\\n  text-align: center;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-color: rgba(184, 166, 118, 0.3);\\n  color: #495057;\\n  transition: all 0.3s ease;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.table-view-enhanced[_ngcontent-%COMP%]   .enhanced-pagination-container[_ngcontent-%COMP%]   .quick-jump[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #5a5a5a;\\n  border-color: rgba(184, 166, 118, 0.3);\\n  transition: all 0.2s ease;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(184, 166, 118, 0.1);\\n  border-color: rgba(184, 166, 118, 0.5);\\n  color: #B8A676;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  background-color: #fff;\\n  border-color: rgba(184, 166, 118, 0.2);\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.selection-options[_ngcontent-%COMP%]   .selected-houses-preview[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  margin-bottom: 1rem;\\n  border: 1px solid transparent;\\n  border-radius: 0.375rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert.alert-info[_ngcontent-%COMP%] {\\n  color: #0c5460;\\n  background-color: #d1ecf1;\\n  border-color: #bee5eb;\\n}\\n.selection-options[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n}\\n\\n@media (max-width: 992px) {\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n    justify-content: center;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    border-radius: 0.375rem 0.375rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start !important;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n    margin-top: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.75rem;\\n    text-align: left !important;\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .flex-fill[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .col-lg-4[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .mx-2[_ngcontent-%COMP%] {\\n    margin: 0.25rem 0 !important;\\n    text-align: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .display-mode-options[_ngcontent-%COMP%]   .form-check[_ngcontent-%COMP%] {\\n    margin-bottom: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .data-summary[_ngcontent-%COMP%] {\\n    text-align: center;\\n    margin-bottom: 0.5rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.25rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3) {\\n    display: none;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .integrated-header[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n    border-radius: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n    border-radius: 0.25rem 0.25rem 0 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-title[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n    margin-right: 0.25rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .page-description[_ngcontent-%COMP%] {\\n    font-size: 0.75rem;\\n    display: none;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%] {\\n    margin-top: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%] {\\n    font-size: 0.85rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .current-case[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .current-info[_ngcontent-%COMP%]   .data-count[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.5rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .primary-filters[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: center;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%] {\\n    padding: 0.75rem;\\n  }\\n  .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .integrated-header[_ngcontent-%COMP%]   .advanced-filters[_ngcontent-%COMP%]   .col-lg-2[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4) {\\n    display: none;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #5a5a5a;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.btn-outline-primary[_ngcontent-%COMP%] {\\n  color: #B8A676;\\n  border-color: #B8A676;\\n  transition: all 0.3s ease;\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #B8A676 0%, #A69660 100%);\\n  border-color: #B8A676;\\n  color: white;\\n  transform: translateY(-1px);\\n  box-shadow: 0 3px 6px rgba(184, 166, 118, 0.3);\\n}\\n.btn-outline-primary[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n\\n.btn-secondary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-color: #dee2e6;\\n  color: #6c757d;\\n}\\n.btn-secondary[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n  color: #5a5a5a;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #D4B96A 0%, #C4A85A 100%);\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #F44336 0%, #E53935 100%);\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #9E9E9E 0%, #757575 100%);\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #E0E0E0 0%, #BDBDBD 100%);\\n}\\n\\n  nb-select.appearance-outline .select-button {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-select.appearance-outline .select-button:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-form-field.appearance-outline .form-control {\\n  border-color: rgba(184, 166, 118, 0.3);\\n}\\n  nb-form-field.appearance-outline .form-control:focus {\\n  border-color: #B8A676;\\n  box-shadow: 0 0 0 0.2rem rgba(184, 166, 118, 0.25);\\n}\\n  nb-checkbox .customised-control-input:checked ~ .customised-control-indicator {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n  nb-calendar-day-cell.selected {\\n  background-color: #B8A676;\\n  border-color: #B8A676;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n      data: {\n        animation: [trigger('slideInOut', [transition(':enter', [style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }), animate('300ms ease-out', style({\n          opacity: 1,\n          transform: 'translateY(0)'\n        }))]), transition(':leave', [animate('300ms ease-in', style({\n          opacity: 0,\n          transform: 'translateY(-10px)'\n        }))])])]\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "trigger", "style", "transition", "animate", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "floor_r6", "ɵɵlistener", "SettingTimePeriodComponent_div_55_button_37_Template_button_click_0_listener", "ɵɵrestoreView", "_r7", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "clearAllFilters", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_55_Template_input_ngModelChange_5_listener", "$event", "_r4", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "onSearch", "SettingTimePeriodComponent_div_55_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_55_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_div_55_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_div_55_Template_nb_select_selectedChange_21_listener", "ɵɵtemplate", "SettingTimePeriodComponent_div_55_nb_option_24_Template", "SettingTimePeriodComponent_div_55_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_div_55_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_div_55_button_37_Template", "ɵɵtwoWayProperty", "availableFloors", "hasActiveFilters", "ɵɵtextInterpolate", "selectedHouses", "length", "SettingTimePeriodComponent_div_56_div_32_Template_button_click_1_listener", "_r9", "openBatchSetting", "SettingTimePeriodComponent_div_56_div_32_Template_button_click_6_listener", "clearSelection", "ɵɵpipeBind2", "house_r11", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_div_56_tr_74_Template_nb_checkbox_ngModelChange_2_listener", "_r10", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_div_56_tr_74_small_7_Template", "SettingTimePeriodComponent_div_56_tr_74_span_16_Template", "SettingTimePeriodComponent_div_56_tr_74_span_17_Template", "SettingTimePeriodComponent_div_56_tr_74_span_20_Template", "SettingTimePeriodComponent_div_56_tr_74_span_21_Template", "SettingTimePeriodComponent_div_56_tr_74_Template_button_click_30_listener", "dialog_r12", "ɵɵreference", "openModel", "ɵɵclassProp", "CHouseId", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusIcon", "getStatusText", "SettingTimePeriodComponent_div_56_div_75_li_21_Template_button_click_1_listener", "page_r15", "_r14", "goToPage", "currentPage", "SettingTimePeriodComponent_div_56_div_75_Template_button_click_16_listener", "_r13", "SettingTimePeriodComponent_div_56_div_75_Template_button_click_19_listener", "SettingTimePeriodComponent_div_56_div_75_li_21_Template", "SettingTimePeriodComponent_div_56_div_75_Template_button_click_23_listener", "SettingTimePeriodComponent_div_56_div_75_Template_button_click_26_listener", "totalPages", "SettingTimePeriodComponent_div_56_div_75_Template_input_ngModelChange_30_listener", "jumpToPage", "SettingTimePeriodComponent_div_56_div_75_Template_input_keyup_enter_30_listener", "jumpToPageAction", "SettingTimePeriodComponent_div_56_div_75_Template_button_click_31_listener", "ɵɵtextInterpolate3", "Math", "min", "filteredHouses", "getVisiblePages", "SettingTimePeriodComponent_div_56_span_10_Template", "SettingTimePeriodComponent_div_56_Template_button_click_13_listener", "_r8", "setQuickFilter", "SettingTimePeriodComponent_div_56_Template_button_click_16_listener", "SettingTimePeriodComponent_div_56_Template_button_click_19_listener", "SettingTimePeriodComponent_div_56_Template_button_click_22_listener", "SettingTimePeriodComponent_div_56_Template_nb_checkbox_ngModelChange_29_listener", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_div_56_div_32_Template", "SettingTimePeriodComponent_div_56_Template_button_click_35_listener", "exportData", "SettingTimePeriodComponent_div_56_Template_nb_checkbox_ngModelChange_46_listener", "SettingTimePeriodComponent_div_56_Template_th_click_47_listener", "sort", "SettingTimePeriodComponent_div_56_Template_th_click_51_listener", "SettingTimePeriodComponent_div_56_Template_th_click_55_listener", "SettingTimePeriodComponent_div_56_Template_th_click_59_listener", "SettingTimePeriodComponent_div_56_Template_th_click_63_listener", "SettingTimePeriodComponent_div_56_tr_74_Template", "SettingTimePeriodComponent_div_56_div_75_Template", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "selectedBuildingForBatch", "name", "house_r17", "SettingTimePeriodComponent_ng_template_59_div_28_span_4_Template", "SettingTimePeriodComponent_ng_template_59_div_28_span_5_Template", "slice", "SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r22", "_r21", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_nb_checkbox_1_Template", "floor_r20", "houses", "SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r19", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_59_div_29_div_3_div_1_Template", "floors", "SettingTimePeriodComponent_ng_template_59_div_29_Template_nb_checkbox_ngModelChange_1_listener", "_r18", "batchSettings", "applyToAll", "SettingTimePeriodComponent_ng_template_59_div_29_div_3_Template", "flattenedHouses", "SettingTimePeriodComponent_ng_template_59_span_3_Template", "SettingTimePeriodComponent_ng_template_59_span_4_Template", "SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_14_listener", "_r16", "startDate", "SettingTimePeriodComponent_ng_template_59_Template_input_ngModelChange_21_listener", "endDate", "SettingTimePeriodComponent_ng_template_59_div_28_Template", "SettingTimePeriodComponent_ng_template_59_div_29_Template", "SettingTimePeriodComponent_ng_template_59_Template_button_click_31_listener", "ref_r23", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_59_Template_button_click_33_listener", "onBatchSubmit", "SettingTimePeriodComponent_ng_template_59_span_35_Template", "batchStartDate_r24", "batchEndDate_r25", "SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_14_listener", "_r26", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_63_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_63_Template_button_click_25_listener", "ref_r27", "SettingTimePeriodComponent_ng_template_63_Template_button_click_27_listener", "onSubmit", "changeStartDate_r28", "changeEndDate_r29", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "loading", "showAdvancedFilters", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "toggleAdvancedFilters", "getActiveFiltersCount", "count", "resetFilters", "status", "for<PERSON>ach", "house", "getHouseStatus", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "household", "CHouses", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "onBuildCaseChange", "resetAllStates", "houseChangeDates", "convertedHouseArray", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "now", "today", "getFullYear", "getMonth", "getDate", "isNaN", "getTime", "console", "warn", "start", "end", "houseId", "startDateOnly", "endDateOnly", "error", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "hasSelectedHouses", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "debugStatusCounts", "statusCounts", "active", "pending", "expired", "disabled", "hasOwnProperty", "log", "toISOString", "split", "previouslySelectedIds", "updateSelectAllState", "updatePagination", "every", "ceil", "startIndex", "endIndex", "page", "pages", "maxVisible", "max", "i", "updateSelectedHouses", "field", "aValue", "bValue", "Number", "_index", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_23_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON><PERSON>_23_listener", "SettingTimePeriodComponent_nb_option_24_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_28_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_28_listener", "SettingTimePeriodComponent_nb_option_31_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_38_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_45_listener", "SettingTimePeriodComponent_Template_button_click_50_listener", "SettingTimePeriodComponent_Template_button_click_53_listener", "SettingTimePeriodComponent_div_55_Template", "SettingTimePeriodComponent_div_56_Template", "SettingTimePeriodComponent_div_57_Template", "SettingTimePeriodComponent_div_58_Template", "SettingTimePeriodComponent_ng_template_59_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_61_Template", "SettingTimePeriodComponent_ng_template_63_Template", "StartDate_r30", "EndDate_r31", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MinValidator", "MaxValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles", "animation", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { trigger, state, style, transition, animate } from '@angular/animations';\r\n\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n  animations: [\r\n    trigger('slideInOut', [\r\n      transition(':enter', [\r\n        style({ opacity: 0, transform: 'translateY(-10px)' }),\r\n        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))\r\n      ]),\r\n      transition(':leave', [\r\n        animate('300ms ease-in', style({ opacity: 0, transform: 'translateY(-10px)' }))\r\n      ])\r\n    ])\r\n  ]\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 表格視圖相關屬性\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  override pageSize: number = 50;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  // 新增的UI控制屬性\r\n  showAdvancedFilters: boolean = false;\r\n  jumpToPage: number = 1;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  // 新增的UI控制方法\r\n  toggleAdvancedFilters(): void {\r\n    this.showAdvancedFilters = !this.showAdvancedFilters;\r\n  }\r\n\r\n  hasActiveFilters(): boolean {\r\n    return !!(this.filterOptions.searchKeyword ||\r\n      this.filterOptions.statusFilter ||\r\n      this.filterOptions.floorFilter);\r\n  }\r\n\r\n  getActiveFiltersCount(): number {\r\n    let count = 0;\r\n    if (this.filterOptions.searchKeyword) count++;\r\n    if (this.filterOptions.statusFilter) count++;\r\n    if (this.filterOptions.floorFilter) count++;\r\n    return count;\r\n  }\r\n\r\n  resetFilters(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: this.searchQuery.CBuildCaseSelected,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    };\r\n    this.selectedBuilding = '';\r\n    this.clearAllFilters();\r\n  }\r\n\r\n  clearAllFilters(): void {\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n    this.onSearch();\r\n  }\r\n\r\n  setQuickFilter(status: string): void {\r\n    if (this.filterOptions.statusFilter === status) {\r\n      this.filterOptions.statusFilter = '';\r\n    } else {\r\n      this.filterOptions.statusFilter = status;\r\n    }\r\n    this.onSearch();\r\n  }\r\n\r\n  clearSelection(): void {\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n    this.flattenedHouses.forEach(house => house.selected = false);\r\n  }\r\n\r\n  getStatusIcon(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    switch (status) {\r\n      case 'active': return 'fas fa-play-circle';\r\n      case 'pending': return 'fas fa-clock';\r\n      case 'expired': return 'fas fa-times-circle';\r\n      case 'not-set': return 'fas fa-exclamation-triangle';\r\n      case 'disabled': return 'fas fa-ban';\r\n      default: return 'fas fa-exclamation-triangle';\r\n    }\r\n  }\r\n\r\n  jumpToPageAction(): void {\r\n    if (this.jumpToPage && this.jumpToPage >= 1 && this.jumpToPage <= this.totalPages) {\r\n      this.goToPage(this.jumpToPage);\r\n    }\r\n  }\r\n\r\n\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  // 新增：建案變更處理\r\n  onBuildCaseChange() {\r\n    // 重置所有相關狀態\r\n    this.resetAllStates();\r\n    // 執行查詢\r\n    this.getHouseChangeDate();\r\n  }\r\n\r\n  // 新增：重置所有狀態\r\n  resetAllStates() {\r\n    // 重置數據\r\n    this.houseChangeDates = [];\r\n    this.convertedHouseArray = [];\r\n    this.buildingGroups = [];\r\n    this.flattenedHouses = [];\r\n    this.filteredHouses = [];\r\n    this.paginatedHouses = [];\r\n    this.selectedHouses = [];\r\n\r\n    // 重置篩選條件\r\n    this.filterOptions = {\r\n      searchKeyword: '',\r\n      statusFilter: '',\r\n      floorFilter: '',\r\n      buildingFilter: ''\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    this.selectAll = false;\r\n    this.selectedBuilding = '';\r\n\r\n    // 重置分頁\r\n    this.currentPage = 1;\r\n    this.totalPages = 1;\r\n\r\n    // 重置可用選項\r\n    this.buildingOptions = [];\r\n    this.availableFloors = [];\r\n\r\n    // 重置排序\r\n    this.sortField = '';\r\n    this.sortDirection = 'asc';\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    // 如果沒有選擇建案，直接返回\r\n    if (!this.searchQuery.CBuildCaseSelected?.cID) {\r\n      this.loading = false;\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 修改：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    // 重置選擇狀態\r\n    this.selectedHouses.forEach(house => house.selected = false);\r\n    this.selectedHouses = [];\r\n    this.selectAll = false;\r\n\r\n    // 重置分頁到第一頁\r\n    this.currentPage = 1;\r\n\r\n    // 重置樓層篩選\r\n    this.filterOptions.floorFilter = '';\r\n\r\n    // 更新可用樓層\r\n    this.updateAvailableFloors();\r\n\r\n    // 設定棟別篩選並執行搜尋\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n    this.onSearch();\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 修復：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'pending':\r\n        return status === 'pending';\r\n      case 'expired':\r\n        return status === 'expired';\r\n      case 'not-set':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true; // 全部狀態\r\n    }\r\n  }\r\n\r\n  // 修復：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    // 檢查是否有設定開放時段\r\n    if (!house.CChangeStartDate || !house.CChangeEndDate ||\r\n      house.CChangeStartDate === '' || house.CChangeEndDate === '') {\r\n      return 'not-set';\r\n    }\r\n\r\n    try {\r\n      // 處理日期字串，支援多種格式\r\n      const now = new Date();\r\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\r\n\r\n      // 解析開始日期\r\n      let startDate: Date;\r\n      if (house.CChangeStartDate.includes('T')) {\r\n        startDate = new Date(house.CChangeStartDate);\r\n      } else {\r\n        startDate = new Date(house.CChangeStartDate + 'T00:00:00');\r\n      }\r\n\r\n      // 解析結束日期\r\n      let endDate: Date;\r\n      if (house.CChangeEndDate.includes('T')) {\r\n        endDate = new Date(house.CChangeEndDate);\r\n      } else {\r\n        endDate = new Date(house.CChangeEndDate + 'T23:59:59');\r\n      }\r\n\r\n      // 檢查日期有效性\r\n      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {\r\n        console.warn('Invalid date format:', {\r\n          start: house.CChangeStartDate,\r\n          end: house.CChangeEndDate,\r\n          houseId: house.CHouseId\r\n        });\r\n        return 'not-set';\r\n      }\r\n\r\n      // 轉換為日期比較（不含時間）\r\n      const startDateOnly = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());\r\n      const endDateOnly = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());\r\n\r\n      // 判斷狀態\r\n      if (today < startDateOnly) {\r\n        return 'pending'; // 待開放\r\n      } else if (today >= startDateOnly && today <= endDateOnly) {\r\n        return 'active'; // 進行中\r\n      } else {\r\n        return 'expired'; // 已過期\r\n      }\r\n    } catch (error) {\r\n      console.error('Error parsing dates:', error, house);\r\n      return 'not-set';\r\n    }\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 修改：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n\r\n    // 如果有選中的房屋，使用選中的房屋；否則使用全部\r\n    const hasSelectedHouses = this.selectedHouses.length > 0;\r\n\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !hasSelectedHouses && !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 修改：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.flattenedHouses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          housesToUpdate.push({\r\n            CHouseId: house.CHouseId,\r\n            CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n            CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n          });\r\n        }\r\n      });\r\n    } else {\r\n      // 使用已選擇的戶別\r\n      if (this.selectedHouses.length > 0) {\r\n        this.selectedHouses.forEach(house => {\r\n          if (house.CHouseId) {\r\n            housesToUpdate.push({\r\n              CHouseId: house.CHouseId,\r\n              CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n              CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n            });\r\n          }\r\n        });\r\n      } else if (this.selectedBuildingForBatch) {\r\n        // 如果沒有選擇的戶別，使用舊的邏輯\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        // 清除選擇狀態\r\n        this.selectedHouses.forEach(house => house.selected = false);\r\n        this.selectedHouses = [];\r\n        this.selectAll = false;\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n\r\n    // 調試：輸出狀態統計\r\n    this.debugStatusCounts();\r\n  }\r\n\r\n  // 調試：輸出狀態統計\r\n  private debugStatusCounts() {\r\n    const statusCounts = {\r\n      active: 0,\r\n      pending: 0,\r\n      expired: 0,\r\n      'not-set': 0,\r\n      disabled: 0\r\n    };\r\n\r\n    this.flattenedHouses.forEach(house => {\r\n      const status = this.getHouseStatus(house);\r\n      if (statusCounts.hasOwnProperty(status)) {\r\n        statusCounts[status as keyof typeof statusCounts]++;\r\n      }\r\n    });\r\n\r\n    console.log('狀態統計:', statusCounts);\r\n    console.log('今天日期:', new Date().toISOString().split('T')[0]);\r\n  }\r\n\r\n\r\n\r\n  // 修改：搜尋和篩選\r\n  onSearch() {\r\n    // 記錄篩選前的已選擇項目\r\n    const previouslySelectedIds = this.selectedHouses.map(house => house.CHouseId);\r\n\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 更新選擇狀態：只保留仍在篩選結果中的已選項目\r\n    this.selectedHouses = this.filteredHouses.filter(house =>\r\n      previouslySelectedIds.includes(house.CHouseId)\r\n    );\r\n\r\n    // 更新扁平化資料中的選擇狀態\r\n    this.flattenedHouses.forEach(house => {\r\n      house.selected = this.selectedHouses.some(selected => selected.CHouseId === house.CHouseId);\r\n    });\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新全選狀態\r\n  private updateSelectAllState() {\r\n    if (this.paginatedHouses.length === 0) {\r\n      this.selectAll = false;\r\n    } else {\r\n      this.selectAll = this.paginatedHouses.every(house =>\r\n        !house.CHouseId || house.selected\r\n      );\r\n    }\r\n  }\r\n\r\n  // 修改：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n\r\n    // 更新全選狀態\r\n    this.updateSelectAllState();\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <!-- 優化的Header區域 -->\r\n    <div class=\"page-header-optimized\">\r\n      <!-- 頁面標題和幫助 -->\r\n      <div class=\"page-title-section\">\r\n        <div class=\"d-flex align-items-center justify-content-between\">\r\n          <div class=\"title-group\">\r\n            <h5 class=\"page-title mb-1\">\r\n              <i class=\"fas fa-clock me-2\"></i>選樣開放時段設定\r\n            </h5>\r\n            <p class=\"page-subtitle text-muted mb-0\">管理各戶別的選樣開放時間範圍</p>\r\n          </div>\r\n          <div class=\"help-section\">\r\n            <button class=\"btn btn-outline-secondary btn-sm\" type=\"button\" data-bs-toggle=\"tooltip\"\r\n              title=\"設定各戶別的選樣開放時段，控制客戶選樣權限\">\r\n              <i class=\"fas fa-question-circle\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 緊湊的主要篩選區域 -->\r\n      <div class=\"compact-filters\">\r\n        <div class=\"row g-3 align-items-end\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <label class=\"form-label small fw-medium\">建案 <span class=\"text-danger\">*</span></label>\r\n            <nb-select placeholder=\"請選擇建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\"\r\n              (selectedChange)=\"onBuildCaseChange()\" size=\"small\">\r\n              <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                {{ case.CBuildCaseName }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <label class=\"form-label small fw-medium\">棟別</label>\r\n            <nb-select placeholder=\"全部棟別\" [(ngModel)]=\"selectedBuilding\" (selectedChange)=\"onBuildingChange()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部棟別</nb-option>\r\n              <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n                {{ building }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-4 col-md-5\">\r\n            <label class=\"form-label small fw-medium\">開放日期範圍</label>\r\n            <div class=\"date-range-group\">\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"開始日期\" [nbDatepicker]=\"StartDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n                <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n              <span class=\"date-separator\">~</span>\r\n              <nb-form-field size=\"small\">\r\n                <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n                <input nbInput type=\"text\" placeholder=\"結束日期\" [nbDatepicker]=\"EndDate\"\r\n                  [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n                <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n              </nb-form-field>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"action-buttons\">\r\n              <button class=\"btn btn-primary\" (click)=\"getHouseChangeDate()\" [disabled]=\"loading\">\r\n                <i class=\"fas fa-search me-1\"></i>查詢\r\n              </button>\r\n              <button class=\"btn btn-outline-secondary ms-2\" (click)=\"resetFilters()\" [disabled]=\"loading\"\r\n                title=\"重置篩選條件\">\r\n                <i class=\"fas fa-undo\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n\r\n\r\n      <!-- 進階篩選區域 -->\r\n      <div class=\"advanced-filters-panel\" *ngIf=\"flattenedHouses.length > 0\">\r\n        <div class=\"row g-3 align-items-center\">\r\n          <div class=\"col-lg-3 col-md-4\">\r\n            <nb-form-field size=\"small\">\r\n              <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n              <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n                (ngModelChange)=\"onSearch()\">\r\n            </nb-form-field>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部狀態</nb-option>\r\n              <nb-option value=\"active\">進行中</nb-option>\r\n              <nb-option value=\"pending\">待開放</nb-option>\r\n              <nb-option value=\"expired\">已過期</nb-option>\r\n              <nb-option value=\"not-set\">未設定</nb-option>\r\n              <nb-option value=\"disabled\">已停用</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-3\">\r\n            <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\"\r\n              size=\"small\">\r\n              <nb-option value=\"\">全部樓層</nb-option>\r\n              <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n                {{ floor }}F\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-2 col-md-2\">\r\n            <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\" size=\"small\">\r\n              <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n              <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n              <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n              <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n            </nb-select>\r\n          </div>\r\n\r\n          <div class=\"col-lg-3 col-md-12\">\r\n            <div class=\"filter-actions\">\r\n              <button class=\"btn btn-outline-danger btn-sm\" (click)=\"clearAllFilters()\" *ngIf=\"hasActiveFilters()\">\r\n                <i class=\"fas fa-times me-1\"></i>清除篩選\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的表格視圖 -->\r\n    <div class=\"table-view-enhanced mt-4\" *ngIf=\"flattenedHouses.length > 0\">\r\n      <!-- 資料統計和快速篩選 -->\r\n      <div class=\"data-summary-bar\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"summary-info\">\r\n            <span class=\"total-count\">\r\n              <i class=\"fas fa-database me-1\"></i>\r\n              共 <strong>{{ filteredHouses.length }}</strong> 筆資料\r\n            </span>\r\n            <span class=\"selected-count\" *ngIf=\"selectedHouses.length > 0\">\r\n              <i class=\"fas fa-check-square me-1 text-primary\"></i>\r\n              已選 <strong class=\"text-primary\">{{ selectedHouses.length }}</strong> 筆\r\n            </span>\r\n          </div>\r\n          <div class=\"quick-filters\">\r\n            <div class=\"btn-group btn-group-sm\" role=\"group\">\r\n              <button type=\"button\" class=\"btn\" [class.btn-primary]=\"filterOptions.statusFilter === 'active'\"\r\n                [class.btn-outline-primary]=\"filterOptions.statusFilter !== 'active'\"\r\n                (click)=\"setQuickFilter('active')\">\r\n                <i class=\"fas fa-play-circle me-1\"></i>進行中\r\n              </button>\r\n              <button type=\"button\" class=\"btn\" [class.btn-warning]=\"filterOptions.statusFilter === 'pending'\"\r\n                [class.btn-outline-warning]=\"filterOptions.statusFilter !== 'pending'\"\r\n                (click)=\"setQuickFilter('pending')\">\r\n                <i class=\"fas fa-clock me-1\"></i>待開放\r\n              </button>\r\n              <button type=\"button\" class=\"btn\" [class.btn-danger]=\"filterOptions.statusFilter === 'expired'\"\r\n                [class.btn-outline-danger]=\"filterOptions.statusFilter !== 'expired'\"\r\n                (click)=\"setQuickFilter('expired')\">\r\n                <i class=\"fas fa-times-circle me-1\"></i>已過期\r\n              </button>\r\n              <button type=\"button\" class=\"btn btn-not-set\"\r\n                [class.btn-not-set-active]=\"filterOptions.statusFilter === 'not-set'\"\r\n                [class.btn-not-set-outline]=\"filterOptions.statusFilter !== 'not-set'\"\r\n                (click)=\"setQuickFilter('not-set')\">\r\n                <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的工具列 -->\r\n      <div class=\"enhanced-toolbar\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"batch-operations\">\r\n            <div class=\"selection-controls\">\r\n              <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\" class=\"select-all-checkbox\">\r\n                <span class=\"fw-medium\">全選</span>\r\n              </nb-checkbox>\r\n              <div class=\"batch-actions ms-3\" *ngIf=\"selectedHouses.length > 0\">\r\n                <button class=\"btn btn-warning btn-sm\" (click)=\"openBatchSetting()\" title=\"批次設定選中的戶別開放時段\">\r\n                  <i class=\"fas fa-cogs me-1\"></i>批次設定\r\n                  <span class=\"badge bg-light text-dark ms-1\">{{ selectedHouses.length }}</span>\r\n                </button>\r\n                <button class=\"btn btn-outline-danger btn-sm ms-2\" (click)=\"clearSelection()\" title=\"清除選擇\">\r\n                  <i class=\"fas fa-times me-1\"></i>清除選擇\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"table-controls\">\r\n            <div class=\"d-flex align-items-center gap-2\">\r\n              <button class=\"btn btn-outline-success btn-sm\" [disabled]=\"filteredHouses.length === 0\"\r\n                (click)=\"exportData()\" title=\"匯出資料\">\r\n                <i class=\"fas fa-download me-1\"></i>匯出\r\n              </button>\r\n              <div class=\"pagination-summary\">\r\n                <small class=\"text-muted\">\r\n                  顯示 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                  {{ Math.min(currentPage * pageSize, filteredHouses.length) }} /\r\n                  共 {{ filteredHouses.length }} 筆\r\n                </small>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 優化的表格 -->\r\n      <div class=\"enhanced-table-container\">\r\n        <table class=\"table table-hover enhanced-table\">\r\n          <thead class=\"enhanced-table-header\">\r\n            <tr>\r\n              <th width=\"50\" class=\"text-center\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  戶型\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  棟別\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable text-center\">\r\n                <div class=\"header-content\">\r\n                  樓層\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"140\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  開始日期\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"140\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                <div class=\"header-content\">\r\n                  結束日期\r\n                  <i class=\"fas fa-sort sort-icon\"\r\n                    [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                    [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n                </div>\r\n              </th>\r\n              <th width=\"120\" class=\"text-center\">\r\n                <div class=\"header-content\">\r\n                  狀態\r\n                </div>\r\n              </th>\r\n              <th width=\"100\" class=\"text-center\">\r\n                <div class=\"header-content\">\r\n                  操作\r\n                </div>\r\n              </th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n              [class.table-row-selected]=\"house.selected\" [class.table-row-disabled]=\"!house.CHouseId\">\r\n              <td class=\"text-center\">\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </td>\r\n              <td>\r\n                <div class=\"house-info\">\r\n                  <span class=\"house-name fw-medium\">{{ house.CHouseHold }}</span>\r\n                  <small class=\"text-muted d-block\" *ngIf=\"!house.CHouseId\">無資料</small>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <span class=\"building-name\">{{ house.CBuildingName }}</span>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <span class=\"floor-badge\">{{ house.CFloor }}F</span>\r\n              </td>\r\n              <td>\r\n                <div class=\"date-info\">\r\n                  <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                    <i class=\"fas fa-calendar me-1 text-success\"></i>\r\n                    {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeStartDate\" class=\"not-set-text\">\r\n                    <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td>\r\n                <div class=\"date-info\">\r\n                  <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                    <i class=\"fas fa-calendar me-1 text-danger\"></i>\r\n                    {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeEndDate\" class=\"not-set-text\">\r\n                    <i class=\"fas fa-exclamation-triangle me-1\"></i>未設定\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"status-display\">\r\n                  <span class=\"enhanced-status-badge\" [class]=\"getStatusClass(house)\">\r\n                    <i class=\"status-icon\" [class]=\"getStatusIcon(house)\"></i>\r\n                    <span class=\"status-text\">{{ getStatusText(house) }}</span>\r\n                  </span>\r\n                </div>\r\n              </td>\r\n              <td class=\"text-center\">\r\n                <div class=\"action-buttons\">\r\n                  <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                    (click)=\"openModel(dialog, house)\" title=\"編輯時段設定\">\r\n                    <i class=\"fas fa-edit\"></i>\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 優化的分頁控制 -->\r\n      <div class=\"enhanced-pagination-container\" *ngIf=\"totalPages > 1\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"pagination-info-detailed\">\r\n            <span class=\"text-muted\">\r\n              第 <strong>{{ currentPage }}</strong> 頁，共 <strong>{{ totalPages }}</strong> 頁\r\n              <span class=\"ms-2\">\r\n                (顯示第 {{ (currentPage - 1) * pageSize + 1 }} -\r\n                {{ Math.min(currentPage * pageSize, filteredHouses.length) }} 筆，\r\n                共 {{ filteredHouses.length }} 筆)\r\n              </span>\r\n            </span>\r\n          </div>\r\n\r\n          <nav class=\"pagination-nav\">\r\n            <ul class=\"pagination pagination-sm mb-0\">\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\" title=\"第一頁\">\r\n                  <i class=\"fas fa-angle-double-left\"></i>\r\n                </button>\r\n              </li>\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n                <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\"\r\n                  title=\"上一頁\">\r\n                  <i class=\"fas fa-angle-left\"></i>\r\n                </button>\r\n              </li>\r\n\r\n              <!-- 頁碼顯示 -->\r\n              <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n                <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n              </li>\r\n\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\" [disabled]=\"currentPage === totalPages\"\r\n                  title=\"下一頁\">\r\n                  <i class=\"fas fa-angle-right\"></i>\r\n                </button>\r\n              </li>\r\n              <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n                <button class=\"page-link\" (click)=\"goToPage(totalPages)\" [disabled]=\"currentPage === totalPages\"\r\n                  title=\"最後一頁\">\r\n                  <i class=\"fas fa-angle-double-right\"></i>\r\n                </button>\r\n              </li>\r\n            </ul>\r\n          </nav>\r\n\r\n          <!-- 快速跳轉 -->\r\n          <div class=\"quick-jump\">\r\n            <div class=\"input-group input-group-sm\" style=\"width: 120px;\">\r\n              <input type=\"number\" class=\"form-control\" placeholder=\"頁碼\" [(ngModel)]=\"jumpToPage\"\r\n                (keyup.enter)=\"jumpToPageAction()\" [min]=\"1\" [max]=\"totalPages\">\r\n              <button class=\"btn btn-outline-secondary\" type=\"button\" (click)=\"jumpToPageAction()\" title=\"跳轉\">\r\n                <i class=\"fas fa-arrow-right\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n\r\n    <!-- 載入中狀態 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <div class=\"spinner-border\" role=\"status\">\r\n              <span class=\"sr-only\">載入中...</span>\r\n            </div>\r\n            <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定\r\n      <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n        - 已選擇 {{ selectedHouses.length }} 個戶別\r\n      </span>\r\n      <span *ngIf=\"selectedBuildingForBatch && selectedHouses.length === 0\">\r\n        - {{ selectedBuildingForBatch.name }}\r\n      </span>\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <!-- 如果有已選擇的戶別，顯示已選擇的資訊 -->\r\n          <div *ngIf=\"selectedHouses.length > 0\" class=\"alert alert-info\">\r\n            <h6>將套用到已選擇的 {{ selectedHouses.length }} 個戶別：</h6>\r\n            <div class=\"selected-houses-preview\">\r\n              <span *ngFor=\"let house of selectedHouses.slice(0, 10); let i = index\"\r\n                class=\"badge badge-primary mr-1 mb-1\">\r\n                {{ house.CHouseHold }} ({{ house.CBuildingName }}-{{ house.CFloor }}F)\r\n              </span>\r\n              <span *ngIf=\"selectedHouses.length > 10\" class=\"text-muted\">\r\n                ...等 {{ selectedHouses.length - 10 }} 個\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 如果沒有已選擇的戶別，顯示選擇選項 -->\r\n          <div *ngIf=\"selectedHouses.length === 0\">\r\n            <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n              全部戶別 ({{ flattenedHouses.length }} 個)\r\n            </nb-checkbox>\r\n            <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n              <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n                <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                  {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n                </nb-checkbox>\r\n                <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                  <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                    [disabled]=\"!house.CHouseId\">\r\n                    {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                  </nb-checkbox>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">\r\n        批次設定\r\n        <span *ngIf=\"selectedHouses.length > 0\">({{ selectedHouses.length }} 個戶別)</span>\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,OAAO,EAASC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAShF,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICetEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IASAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IAkEAT,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAe;IAC9DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,QAAA,OACF;;;;;;IAeAV,EAAA,CAAAC,cAAA,iBAAqG;IAAvDD,EAAA,CAAAW,UAAA,mBAAAC,6EAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IACvElB,EAAA,CAAAmB,SAAA,YAAiC;IAAAnB,EAAA,CAAAE,MAAA,gCACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA1CXH,EAHN,CAAAC,cAAA,cAAuE,cAC7B,cACP,wBACD;IAC1BD,EAAA,CAAAmB,SAAA,kBAAkD;IAClDnB,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAAoB,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAC,aAAA,EAAAJ,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAC,aAAA,GAAAJ,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAyC;IAC/EtB,EAAA,CAAAW,UAAA,2BAAAU,0EAAA;MAAArB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAElC3B,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAGJH,EADF,CAAAC,cAAA,cAA+B,oBAEd;IADeD,EAAA,CAAAoB,gBAAA,2BAAAQ,8EAAAN,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAI,YAAA,EAAAP,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAI,YAAA,GAAAP,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAwC;IAACtB,EAAA,CAAAW,UAAA,4BAAAmB,+EAAA;MAAA9B,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAElG3B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAEd;IADeD,EAAA,CAAAoB,gBAAA,2BAAAW,+EAAAT,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAU,aAAA,CAAAO,WAAA,EAAAV,MAAA,MAAAP,MAAA,CAAAU,aAAA,CAAAO,WAAA,GAAAV,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuC;IAACtB,EAAA,CAAAW,UAAA,4BAAAsB,gFAAA;MAAAjC,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAY,QAAA,EAAU;IAAA,EAAC;IAEjG3B,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAkC,UAAA,KAAAC,uDAAA,wBAAiE;IAIrEnC,EADE,CAAAG,YAAA,EAAY,EACR;IAGJH,EADF,CAAAC,cAAA,eAA+B,qBAC2E;IAA1ED,EAAA,CAAAoB,gBAAA,2BAAAgB,+EAAAd,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAsB,QAAA,EAAAf,MAAA,MAAAP,MAAA,CAAAsB,QAAA,GAAAf,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsB;IAACtB,EAAA,CAAAW,UAAA,4BAAA2B,gFAAA;MAAAtC,EAAA,CAAAa,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAkBF,MAAA,CAAAwB,gBAAA,EAAkB;IAAA,EAAC;IACxFvC,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGJH,EADF,CAAAC,cAAA,eAAgC,eACF;IAC1BD,EAAA,CAAAkC,UAAA,KAAAM,oDAAA,qBAAqG;IAM7GxC,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IA5C0CH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAC,aAAA,CAAyC;IAMrD1B,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAI,YAAA,CAAwC;IAYxC7B,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAU,aAAA,CAAAO,WAAA,CAAuC;IAGtChC,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA2B,eAAA,CAAkB;IAOnB1C,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAsB,QAAA,CAAsB;IACvCrC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,aAAY;IACZJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IAMmDJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA4B,gBAAA,GAAwB;;;;;IAmBrG3C,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAmB,SAAA,aAAqD;IACrDnB,EAAA,CAAAE,MAAA,qBAAG;IAAAF,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,eACvE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAD2BH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA8B,cAAA,CAAAC,MAAA,CAA2B;;;;;;IAwCzD9C,EADF,CAAAC,cAAA,eAAkE,kBAC0B;IAAnDD,EAAA,CAAAW,UAAA,mBAAAoC,0EAAA;MAAA/C,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkC,gBAAA,EAAkB;IAAA,EAAC;IACjEjD,EAAA,CAAAmB,SAAA,aAAgC;IAAAnB,EAAA,CAAAE,MAAA,gCAChC;IAAAF,EAAA,CAAAC,cAAA,gBAA4C;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IACzEF,EADyE,CAAAG,YAAA,EAAO,EACvE;IACTH,EAAA,CAAAC,cAAA,kBAA2F;IAAxCD,EAAA,CAAAW,UAAA,mBAAAuC,0EAAA;MAAAlD,EAAA,CAAAa,aAAA,CAAAmC,GAAA;MAAA,MAAAjC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAoC,cAAA,EAAgB;IAAA,EAAC;IAC3EnD,EAAA,CAAAmB,SAAA,YAAiC;IAAAnB,EAAA,CAAAE,MAAA,gCACnC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAL0CH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA8B,cAAA,CAAAC,MAAA,CAA2B;;;;;IAiGvE9C,EAAA,CAAAC,cAAA,iBAA0D;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;;IAWrEH,EAAA,CAAAC,cAAA,gBAA0D;IACxDD,EAAA,CAAAmB,SAAA,aAAiD;IACjDnB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAoD,WAAA,OAAAC,SAAA,CAAAC,gBAAA,qBACF;;;;;IACAtD,EAAA,CAAAC,cAAA,gBAA2D;IACzDD,EAAA,CAAAmB,SAAA,YAAgD;IAAAnB,EAAA,CAAAE,MAAA,0BAClD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAKPH,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAmB,SAAA,aAAgD;IAChDnB,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAoD,WAAA,OAAAC,SAAA,CAAAE,cAAA,qBACF;;;;;IACAvD,EAAA,CAAAC,cAAA,gBAAyD;IACvDD,EAAA,CAAAmB,SAAA,YAAgD;IAAAnB,EAAA,CAAAE,MAAA,0BAClD;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAlCTH,EAHJ,CAAAC,cAAA,SAC2F,cACjE,uBAEuB;IADhCD,EAAA,CAAAoB,gBAAA,2BAAAoC,sFAAAlC,MAAA;MAAA,MAAA+B,SAAA,GAAArD,EAAA,CAAAa,aAAA,CAAA4C,IAAA,EAAAC,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAA6B,SAAA,CAAAM,QAAA,EAAArC,MAAA,MAAA+B,SAAA,CAAAM,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IACvCtB,EAAA,CAAAW,UAAA,2BAAA6C,sFAAA;MAAAxD,EAAA,CAAAa,aAAA,CAAA4C,IAAA;MAAA,MAAA1C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA6C,sBAAA,EAAwB;IAAA,EAAC;IAC9C5D,EAD+C,CAAAG,YAAA,EAAc,EACxD;IAGDH,EAFJ,CAAAC,cAAA,SAAI,eACsB,gBACa;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAkC,UAAA,IAAA2B,wDAAA,qBAA0D;IAE9D7D,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,SAAI,gBAC0B;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACzD;IAEHH,EADF,CAAAC,cAAA,eAAwB,iBACI;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EACjD;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAkC,UAAA,KAAA4B,wDAAA,oBAA0D,KAAAC,wDAAA,oBAIC;IAI/D/D,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACqB;IAKrBD,EAJA,CAAAkC,UAAA,KAAA8B,wDAAA,oBAAwD,KAAAC,wDAAA,oBAIC;IAI7DjE,EADE,CAAAG,YAAA,EAAM,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,gBACM,iBAC0C;IAClED,EAAA,CAAAmB,SAAA,cAA0D;IAC1DnB,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAG1DF,EAH0D,CAAAG,YAAA,EAAO,EACtD,EACH,EACH;IAGDH,EAFJ,CAAAC,cAAA,eAAwB,eACM,mBAE0B;IAAlDD,EAAA,CAAAW,UAAA,mBAAAuD,0EAAA;MAAA,MAAAb,SAAA,GAAArD,EAAA,CAAAa,aAAA,CAAA4C,IAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,MAAAmD,UAAA,GAAAnE,EAAA,CAAAoE,WAAA;MAAA,OAAApE,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsD,SAAA,CAAAF,UAAA,EAAAd,SAAA,CAAwB;IAAA,EAAC;IAClCrD,EAAA,CAAAmB,SAAA,cAA2B;IAInCnB,EAHM,CAAAG,YAAA,EAAS,EACL,EACH,EACF;;;;;IAvDyCH,EAA5C,CAAAsE,WAAA,uBAAAjB,SAAA,CAAAM,QAAA,CAA2C,wBAAAN,SAAA,CAAAkB,QAAA,CAA6C;IAEzEvE,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAyC,gBAAA,YAAAY,SAAA,CAAAM,QAAA,CAA4B;IAAC3D,EAAA,CAAAI,UAAA,cAAAiD,SAAA,CAAAkB,QAAA,CAA4B;IAKjCvE,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA4C,iBAAA,CAAAS,SAAA,CAAAmB,UAAA,CAAsB;IACtBxE,EAAA,CAAAM,SAAA,EAAqB;IAArBN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAkB,QAAA,CAAqB;IAI9BvE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAA4C,iBAAA,CAAAS,SAAA,CAAAoB,aAAA,CAAyB;IAG3BzE,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,kBAAA,KAAA8C,SAAA,CAAAqB,MAAA,MAAmB;IAIpC1E,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAAiD,SAAA,CAAAC,gBAAA,CAA4B;IAI5BtD,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAC,gBAAA,CAA6B;IAO7BtD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAiD,SAAA,CAAAE,cAAA,CAA0B;IAI1BvD,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAiD,SAAA,CAAAE,cAAA,CAA2B;IAOEvD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAA2E,UAAA,CAAA5D,MAAA,CAAA6D,cAAA,CAAAvB,SAAA,EAA+B;IAC1CrD,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAA2E,UAAA,CAAA5D,MAAA,CAAA8D,aAAA,CAAAxB,SAAA,EAA8B;IAC3BrD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA+D,aAAA,CAAAzB,SAAA,EAA0B;IAMPrD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAAiD,SAAA,CAAAkB,QAAA,CAA4B;;;;;;IAyC7EvE,EADF,CAAAC,cAAA,cAAmG,kBAC9C;IAAzBD,EAAA,CAAAW,UAAA,mBAAAoE,gFAAA;MAAA,MAAAC,QAAA,GAAAhF,EAAA,CAAAa,aAAA,CAAAoE,IAAA,EAAAvB,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAAsE,WAAA,WAAAU,QAAA,KAAAjE,MAAA,CAAAoE,WAAA,CAAqC;IAC7CnF,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAA4C,iBAAA,CAAAoC,QAAA,CAAU;;;;;;IA1BjEhF,EAHN,CAAAC,cAAA,eAAkE,cACD,eACvB,eACX;IACvBD,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BAAI;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,gBAC3E;IAAAF,EAAA,CAAAC,cAAA,iBAAmB;IACjBD,EAAA,CAAAE,MAAA,IAGF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACF,EACH;IAKAH,EAHN,CAAAC,cAAA,gBAA4B,eACgB,eACmB,mBACkC;IAAjED,EAAA,CAAAW,UAAA,mBAAAyE,2EAAA;MAAApF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAC7ClF,EAAA,CAAAmB,SAAA,cAAwC;IAE5CnB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAA2D,mBAE3C;IADYD,EAAA,CAAAW,UAAA,mBAAA2E,2EAAA;MAAAtF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAAoE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3DnF,EAAA,CAAAmB,SAAA,cAAiC;IAErCnB,EADE,CAAAG,YAAA,EAAS,EACN;IAGLH,EAAA,CAAAkC,UAAA,KAAAqD,uDAAA,kBAAmG;IAKjGvF,EADF,CAAAC,cAAA,eAAoE,mBAEpD;IADYD,EAAA,CAAAW,UAAA,mBAAA6E,2EAAA;MAAAxF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAAoE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAE3DnF,EAAA,CAAAmB,SAAA,cAAkC;IAEtCnB,EADE,CAAAG,YAAA,EAAS,EACN;IAEHH,EADF,CAAAC,cAAA,eAAoE,mBAEnD;IADWD,EAAA,CAAAW,UAAA,mBAAA8E,2EAAA;MAAAzF,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmE,QAAA,CAAAnE,MAAA,CAAA2E,UAAA,CAAoB;IAAA,EAAC;IAEtD1F,EAAA,CAAAmB,SAAA,cAAyC;IAIjDnB,EAHM,CAAAG,YAAA,EAAS,EACN,EACF,EACD;IAKFH,EAFJ,CAAAC,cAAA,gBAAwB,gBACwC,kBAEM;IADPD,EAAA,CAAAoB,gBAAA,2BAAAuE,kFAAArE,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA6E,UAAA,EAAAtE,MAAA,MAAAP,MAAA,CAAA6E,UAAA,GAAAtE,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAwB;IACjFtB,EAAA,CAAAW,UAAA,yBAAAkF,gFAAA;MAAA7F,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeF,MAAA,CAAA+E,gBAAA,EAAkB;IAAA,EAAC;IADpC9F,EAAA,CAAAG,YAAA,EACkE;IAClEH,EAAA,CAAAC,cAAA,mBAAgG;IAAxCD,EAAA,CAAAW,UAAA,mBAAAoF,2EAAA;MAAA/F,EAAA,CAAAa,aAAA,CAAAwE,IAAA;MAAA,MAAAtE,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAA+E,gBAAA,EAAkB;IAAA,EAAC;IAClF9F,EAAA,CAAAmB,SAAA,cAAkC;IAK5CnB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAtDYH,EAAA,CAAAM,SAAA,GAAiB;IAAjBN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAAoE,WAAA,CAAiB;IAAsBnF,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAA2E,UAAA,CAAgB;IAE/D1F,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAgG,kBAAA,2BAAAjF,MAAA,CAAAoE,WAAA,QAAApE,MAAA,CAAAsB,QAAA,aAAAtB,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAAnF,MAAA,CAAAoE,WAAA,GAAApE,MAAA,CAAAsB,QAAA,EAAAtB,MAAA,CAAAoF,cAAA,CAAArD,MAAA,4BAAA/B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,cAGF;IAMsB9C,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,OAAoC;IACRnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,OAA8B;IAI1DnF,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,OAAoC;IACMnF,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,OAA8B;IAOvDnF,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAqF,eAAA,GAAoB;IAIrCpG,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAA6C;IACH1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAAuC;IAKjF1F,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAAsE,WAAA,aAAAvD,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAA6C;IACR1F,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoE,WAAA,KAAApE,MAAA,CAAA2E,UAAA,CAAuC;IAWvC1F,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA6E,UAAA,CAAwB;IACpC5F,EAAV,CAAAI,UAAA,UAAS,QAAAW,MAAA,CAAA2E,UAAA,CAAmB;;;;;;IAzPnE1F,EALR,CAAAC,cAAA,cAAyE,cAEzC,cACmC,cACnC,eACE;IACxBD,EAAA,CAAAmB,SAAA,YAAoC;IACpCnB,EAAA,CAAAE,MAAA,eAAE;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,2BACjD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAkC,UAAA,KAAAmE,kDAAA,mBAA+D;IAIjErG,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,eAA2B,eACwB,kBAGV;IAAnCD,EAAA,CAAAW,UAAA,mBAAA2F,oEAAA;MAAAtG,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,QAAQ,CAAC;IAAA,EAAC;IAClCxG,EAAA,CAAAmB,SAAA,aAAuC;IAAAnB,EAAA,CAAAE,MAAA,2BACzC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAW,UAAA,mBAAA8F,oEAAA;MAAAzG,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAiC;IAAAnB,EAAA,CAAAE,MAAA,2BACnC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAEsC;IAApCD,EAAA,CAAAW,UAAA,mBAAA+F,oEAAA;MAAA1G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAwC;IAAAnB,EAAA,CAAAE,MAAA,2BAC1C;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGsC;IAApCD,EAAA,CAAAW,UAAA,mBAAAgG,oEAAA;MAAA3G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAyF,cAAA,CAAe,SAAS,CAAC;IAAA,EAAC;IACnCxG,EAAA,CAAAmB,SAAA,aAAgD;IAAAnB,EAAA,CAAAE,MAAA,2BAClD;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;IAOEH,EAJR,CAAAC,cAAA,eAA8B,eACmC,eAC/B,eACI,uBACyE;IAA1FD,EAAA,CAAAoB,gBAAA,2BAAAwF,iFAAAtF,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8F,SAAA,EAAAvF,MAAA,MAAAP,MAAA,CAAA8F,SAAA,GAAAvF,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuB;IAACtB,EAAA,CAAAW,UAAA,2BAAAiG,iFAAA;MAAA5G,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA+F,iBAAA,EAAmB;IAAA,EAAC;IACxE9G,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EACrB;IACdH,EAAA,CAAAkC,UAAA,KAAA6E,iDAAA,kBAAkE;IAUtE/G,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA4B,eACmB,kBAEL;IAApCD,EAAA,CAAAW,UAAA,mBAAAqG,oEAAA;MAAAhH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAkG,UAAA,EAAY;IAAA,EAAC;IACtBjH,EAAA,CAAAmB,SAAA,aAAoC;IAAAnB,EAAA,CAAAE,MAAA,qBACtC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,eAAgC,iBACJ;IACxBD,EAAA,CAAAE,MAAA,IAGF;IAKVF,EALU,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAAsC,iBACY,iBACT,UAC/B,cACiC,uBAC0C;IAA9DD,EAAA,CAAAoB,gBAAA,2BAAA8F,iFAAA5F,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8F,SAAA,EAAAvF,MAAA,MAAAP,MAAA,CAAA8F,SAAA,GAAAvF,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAuB;IAACtB,EAAA,CAAAW,UAAA,2BAAAuG,iFAAA;MAAAlH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA+F,iBAAA,EAAmB;IAAA,EAAC;IAC5E9G,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,cAA8D;IAA9CD,EAAA,CAAAW,UAAA,mBAAAwG,gEAAA;MAAAnH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1CpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEoF;IAExFnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAiE;IAAjDD,EAAA,CAAAW,UAAA,mBAAA0G,gEAAA;MAAArH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7CpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEuF;IAE3FnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAqE;IAAtDD,EAAA,CAAAW,UAAA,mBAAA2G,gEAAA;MAAAtH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrCpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAmB,SAAA,aAEgF;IAEpFnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAoE;IAApDD,EAAA,CAAAW,UAAA,mBAAA4G,gEAAA;MAAAvH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChDpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAmB,SAAA,aAE0F;IAE9FnB,EADE,CAAAG,YAAA,EAAM,EACH;IACLH,EAAA,CAAAC,cAAA,eAAkE;IAAlDD,EAAA,CAAAW,UAAA,mBAAA6G,gEAAA;MAAAxH,EAAA,CAAAa,aAAA,CAAA0F,GAAA;MAAA,MAAAxF,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAqG,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9CpH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAmB,SAAA,aAEwF;IAE5FnB,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,eAAoC,eACN;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACH;IAEHH,EADF,CAAAC,cAAA,eAAoC,eACN;IAC1BD,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAM,EACH,EACF,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAkC,UAAA,KAAAuF,gDAAA,oBAC2F;IA0DjGzH,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAkC,UAAA,KAAAwF,iDAAA,qBAAkE;IA2DpE1H,EAAA,CAAAG,YAAA,EAAM;;;;IA/PcH,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAA4C,iBAAA,CAAA7B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,CAA2B;IAET9C,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAOzB9C,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAsE,WAAA,gBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,cAA6D,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,cACxB;IAIrC7B,EAAA,CAAAM,SAAA,GAA8D;IAC9FN,EADgC,CAAAsE,WAAA,gBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAA8D,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACxB;IAItC7B,EAAA,CAAAM,SAAA,GAA6D;IAC7FN,EADgC,CAAAsE,WAAA,eAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAA6D,uBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACxB;IAKrE7B,EAAA,CAAAM,SAAA,GAAqE;IACrEN,EADA,CAAAsE,WAAA,uBAAAvD,MAAA,CAAAU,aAAA,CAAAI,YAAA,eAAqE,wBAAAd,MAAA,CAAAU,aAAA,CAAAI,YAAA,eACC;IAc3D7B,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8F,SAAA,CAAuB;IAGH7G,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAcjB9C,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAW,MAAA,CAAAoF,cAAA,CAAArD,MAAA,OAAwC;IAMnF9C,EAAA,CAAAM,SAAA,GAGF;IAHEN,EAAA,CAAAgG,kBAAA,oBAAAjF,MAAA,CAAAoE,WAAA,QAAApE,MAAA,CAAAsB,QAAA,aAAAtB,MAAA,CAAAkF,IAAA,CAAAC,GAAA,CAAAnF,MAAA,CAAAoE,WAAA,GAAApE,MAAA,CAAAsB,QAAA,EAAAtB,MAAA,CAAAoF,cAAA,CAAArD,MAAA,iBAAA/B,MAAA,CAAAoF,cAAA,CAAArD,MAAA,aAGF;IAaa9C,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8F,SAAA,CAAuB;IAMhC7G,EAAA,CAAAM,SAAA,GAA0E;IAC1EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,qBAAA5G,MAAA,CAAA6G,aAAA,WAA0E,iBAAA7G,MAAA,CAAA4G,SAAA,qBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAO7E5H,EAAA,CAAAM,SAAA,GAA6E;IAC7EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,wBAAA5G,MAAA,CAAA6G,aAAA,WAA6E,iBAAA7G,MAAA,CAAA4G,SAAA,wBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAOhF5H,EAAA,CAAAM,SAAA,GAAsE;IACtEN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,iBAAA5G,MAAA,CAAA6G,aAAA,WAAsE,iBAAA7G,MAAA,CAAA4G,SAAA,iBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAOzE5H,EAAA,CAAAM,SAAA,GAAgF;IAChFN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,2BAAA5G,MAAA,CAAA6G,aAAA,WAAgF,iBAAA7G,MAAA,CAAA4G,SAAA,2BAAA5G,MAAA,CAAA6G,aAAA,YACG;IAOnF5H,EAAA,CAAAM,SAAA,GAA8E;IAC9EN,EADA,CAAAsE,WAAA,eAAAvD,MAAA,CAAA4G,SAAA,yBAAA5G,MAAA,CAAA6G,aAAA,WAA8E,iBAAA7G,MAAA,CAAA4G,SAAA,yBAAA5G,MAAA,CAAA6G,aAAA,YACG;IAgBnE5H,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAW,MAAA,CAAA8G,eAAA,CAAoB,iBAAA9G,MAAA,CAAA+G,cAAA,CAAuB;IA8D3B9H,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA2E,UAAA,KAAoB;;;;;IAmE5D1F,EAHN,CAAAC,cAAA,eAAoG,cACzF,mBACO,cACY;IACtBD,EAAA,CAAAmB,SAAA,aAA6C;IAC7CnB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,eAA8C,cACnC,mBACO,cACY,eACoB,gBAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;IASJH,EAAA,CAAAC,cAAA,gBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,2BAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,yBACF;;;;;IACA9C,EAAA,CAAAC,cAAA,WAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,QAAAQ,MAAA,CAAAgH,wBAAA,CAAAC,IAAA,MACF;;;;;IA+BQhI,EAAA,CAAAC,cAAA,gBACwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgG,kBAAA,MAAAiC,SAAA,CAAAzD,UAAA,QAAAyD,SAAA,CAAAxD,aAAA,OAAAwD,SAAA,CAAAvD,MAAA,QACF;;;;;IACA1E,EAAA,CAAAC,cAAA,eAA4D;IAC1DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gBAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,kBACF;;;;;IARF9C,EADF,CAAAC,cAAA,eAAgE,SAC1D;IAAAD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClDH,EAAA,CAAAC,cAAA,eAAqC;IAKnCD,EAJA,CAAAkC,UAAA,IAAAgG,gEAAA,oBACwC,IAAAC,gEAAA,oBAGoB;IAIhEnI,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAVAH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAAO,kBAAA,sDAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,8BAAyC;IAEnB9C,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAA8B,cAAA,CAAAuF,KAAA,QAAgC;IAIjDpI,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,MAAgC;;;;;;IAiBnC9C,EAAA,CAAAC,cAAA,uBAC+B;IADiBD,EAAA,CAAAoB,gBAAA,2BAAAiH,+HAAA/G,MAAA;MAAA,MAAAgH,SAAA,GAAAtI,EAAA,CAAAa,aAAA,CAAA0H,IAAA,EAAA7E,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAA8G,SAAA,CAAA3E,QAAA,EAAArC,MAAA,MAAAgH,SAAA,CAAA3E,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IAE1EtB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAAyC,gBAAA,YAAA6F,SAAA,CAAA3E,QAAA,CAA4B;IAC1E3D,EAAA,CAAAI,UAAA,cAAAkI,SAAA,CAAA/D,QAAA,CAA4B;IAC5BvE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAwI,kBAAA,MAAAF,SAAA,CAAA9D,UAAA,QAAA8D,SAAA,CAAA7D,aAAA,OACF;;;;;IAJFzE,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAkC,UAAA,IAAAuG,yFAAA,2BAC+B;IAGjCzI,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAsI,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhD3I,EADF,CAAAC,cAAA,eAAmF,sBACS;IAA7ED,EAAA,CAAAoB,gBAAA,2BAAAwH,2GAAAtH,MAAA;MAAA,MAAAoH,SAAA,GAAA1I,EAAA,CAAAa,aAAA,CAAAgI,IAAA,EAAAnF,SAAA;MAAA1D,EAAA,CAAAwB,kBAAA,CAAAkH,SAAA,CAAA/E,QAAA,EAAArC,MAAA,MAAAoH,SAAA,CAAA/E,QAAA,GAAArC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAA4B;IAACtB,EAAA,CAAAW,UAAA,2BAAAiI,2GAAA;MAAA,MAAAF,SAAA,GAAA1I,EAAA,CAAAa,aAAA,CAAAgI,IAAA,EAAAnF,SAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAiBF,MAAA,CAAA+H,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvF1I,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAkC,UAAA,IAAA6G,2EAAA,mBAAyD;IAM3D/I,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAyC,gBAAA,YAAAiG,SAAA,CAAA/E,QAAA,CAA4B;IACvC3D,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAwI,kBAAA,MAAAE,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA7F,MAAA,cACF;IACmC9C,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAsI,SAAA,CAAA/E,QAAA,CAAoB;;;;;IAL3D3D,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAkC,UAAA,IAAA+G,qEAAA,mBAAmF;IAWrFjJ,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAW,MAAA,CAAAgH,wBAAA,CAAAmB,MAAA,CAAkC;;;;;;IAJnFlJ,EADF,CAAAC,cAAA,UAAyC,sBACa;IAAvCD,EAAA,CAAAoB,gBAAA,2BAAA+H,+FAAA7H,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAAuI,IAAA;MAAA,MAAArI,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAsI,aAAA,CAAAC,UAAA,EAAAhI,MAAA,MAAAP,MAAA,CAAAsI,aAAA,CAAAC,UAAA,GAAAhI,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsC;IACjDtB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAkC,UAAA,IAAAqH,+DAAA,mBAAgF;IAalFvJ,EAAA,CAAAG,YAAA,EAAM;;;;IAhBSH,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAsI,aAAA,CAAAC,UAAA,CAAsC;IACjDtJ,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,gCAAAQ,MAAA,CAAAyI,eAAA,CAAA1G,MAAA,cACF;IACmB9C,EAAA,CAAAM,SAAA,EAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAW,MAAA,CAAAsI,aAAA,CAAAC,UAAA,IAAAvI,MAAA,CAAAgH,wBAAA,CAA2D;;;;;IAqBlF/H,EAAA,CAAAC,cAAA,WAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxCH,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAO,kBAAA,MAAAQ,MAAA,CAAA8B,cAAA,CAAAC,MAAA,yBAAiC;;;;;;IA1E7E9C,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,iCACA;IAGAF,EAHA,CAAAkC,UAAA,IAAAuH,yDAAA,oBAA6D,IAAAC,yDAAA,oBAGS;IAGxE1J,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,gBAAuC,0BACJ;IAC/BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAAoB,gBAAA,2BAAAuI,mFAAArI,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+I,IAAA;MAAA,MAAA7I,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAsI,aAAA,CAAAQ,SAAA,EAAAvI,MAAA,MAAAP,MAAA,CAAAsI,aAAA,CAAAQ,SAAA,GAAAvI,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAqC;IADvCtB,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAmB,SAAA,4BAAmE;IACrEnB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,0BAAiC;IAC/BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAAoB,gBAAA,2BAAA0I,mFAAAxI,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+I,IAAA;MAAA,MAAA7I,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAAsI,aAAA,CAAAU,OAAA,EAAAzI,MAAA,MAAAP,MAAA,CAAAsI,aAAA,CAAAU,OAAA,GAAAzI,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAmC;IADrCtB,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAmB,SAAA,4BAAiE;IAGvEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAA+B;IAgB7BD,EAdA,CAAAkC,UAAA,KAAA8H,yDAAA,mBAAgE,KAAAC,yDAAA,mBAcvB;IAoB/CjK,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAAW,UAAA,mBAAAuJ,4EAAA;MAAA,MAAAC,OAAA,GAAAnK,EAAA,CAAAa,aAAA,CAAA+I,IAAA,EAAAQ,SAAA;MAAA,MAAArJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsJ,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAACnK,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAAW,UAAA,mBAAA2J,4EAAA;MAAA,MAAAH,OAAA,GAAAnK,EAAA,CAAAa,aAAA,CAAA+I,IAAA,EAAAQ,SAAA;MAAA,MAAArJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAwJ,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAC1DnK,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAkC,UAAA,KAAAsI,0DAAA,oBAAwC;IAG9CxK,EAFI,CAAAG,YAAA,EAAS,EACM,EACT;;;;;;IA3ECH,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAG/B9C,EAAA,CAAAM,SAAA,EAA6D;IAA7DN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAAgH,wBAAA,IAAAhH,MAAA,CAAA8B,cAAA,CAAAC,MAAA,OAA6D;IAWf9C,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAAqK,kBAAA,CAA+B;IAC5EzK,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAsI,aAAA,CAAAQ,SAAA,CAAqC;IAMQ7J,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAAsK,gBAAA,CAA6B;IAC1E1K,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAAsI,aAAA,CAAAU,OAAA,CAAmC;IAWjC/J,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;IAc/B9C,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,OAAiC;IAyBlC9C,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAW,MAAA,CAAA8B,cAAA,CAAAC,MAAA,KAA+B;;;;;IAO5C9C,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAmB,SAAA,qBACiB,mBAGF,0BAGE;IACnBnB,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,eACyB,iBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAAoB,gBAAA,2BAAAuJ,mFAAArJ,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+J,IAAA;MAAA,MAAA7J,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8J,uBAAA,CAAAvH,gBAAA,EAAAhC,MAAA,MAAAP,MAAA,CAAA8J,uBAAA,CAAAvH,gBAAA,GAAAhC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAsD;IAD7EtB,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAmB,SAAA,4BAAoE;IACtEnB,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAmB,SAAA,mBAAoD;IACpDnB,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAAoB,gBAAA,2BAAA0J,mFAAAxJ,MAAA;MAAAtB,EAAA,CAAAa,aAAA,CAAA+J,IAAA;MAAA,MAAA7J,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAwB,kBAAA,CAAAT,MAAA,CAAA8J,uBAAA,CAAAtH,cAAA,EAAAjC,MAAA,MAAAP,MAAA,CAAA8J,uBAAA,CAAAtH,cAAA,GAAAjC,MAAA;MAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;IAAA,EAAoD;IAD3EtB,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAmB,SAAA,4BAAkE;IAGxEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,mBACiB;IAAvBD,EAAA,CAAAW,UAAA,mBAAAoK,4EAAA;MAAA,MAAAC,OAAA,GAAAhL,EAAA,CAAAa,aAAA,CAAA+J,IAAA,EAAAR,SAAA;MAAA,MAAArJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAsJ,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAAChL,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAW,UAAA,mBAAAsK,4EAAA;MAAA,MAAAD,OAAA,GAAAhL,EAAA,CAAAa,aAAA,CAAA+J,IAAA,EAAAR,SAAA;MAAA,MAAArJ,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAmK,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAAChL,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAAwI,kBAAA,KAAAzH,MAAA,CAAA8J,uBAAA,CAAArG,UAAA,SAAAzD,MAAA,CAAA8J,uBAAA,CAAAnG,MAAA,MACE;IAQoC1E,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAA+K,mBAAA,CAAgC;IAC9EnL,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8J,uBAAA,CAAAvH,gBAAA,CAAsD;IAOVtD,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAgL,iBAAA,CAA8B;IAC1EpL,EAAA,CAAAyC,gBAAA,YAAA1B,MAAA,CAAA8J,uBAAA,CAAAtH,cAAA,CAAoD;;;AD5crF,OAAM,MAAO8H,0BAA2B,SAAQvL,aAAa;EAK3DwL,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAUtD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAA3J,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAjB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfsK,cAAc,EAAE;KACjB;IAED;IACA,KAAAjD,aAAa,GAAkB;MAC7BQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,IAAI;MAChBiD,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClB3J,cAAc,EAAE;KACjB;IAED,KAAAkF,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAAyB,eAAe,GAAqB,EAAE;IACtC,KAAArD,cAAc,GAAqB,EAAE;IACrC,KAAA0B,eAAe,GAAqB,EAAE;IACtC,KAAAhF,cAAc,GAAqB,EAAE;IACrC,KAAAgE,SAAS,GAAY,KAAK;IAC1B,KAAA4F,OAAO,GAAY,KAAK;IAExB;IACA,KAAAtH,WAAW,GAAW,CAAC;IACd,KAAA9C,QAAQ,GAAW,EAAE;IAC9B,KAAAqD,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAiC,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAA3B,IAAI,GAAGA,IAAI;IAEX;IACA,KAAAyG,mBAAmB,GAAY,KAAK;IACpC,KAAA9G,UAAU,GAAW,CAAC;IAtFpB,IAAI,CAACiF,uBAAuB,GAAG;MAC7BvH,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBmB,MAAM,EAAEiI,SAAS;MACjBnI,UAAU,EAAE,EAAE;MACdD,QAAQ,EAAEoI;KACX;IAED,IAAI,CAACb,aAAa,CAACc,OAAO,EAAE,CAACC,IAAI,CAC/BxN,GAAG,CAAEyN,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAAChB,eAAe,GAAGe,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAyESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACrB,gBAAgB,CAAC,CAAC,CAAC;MAC/C1I,gBAAgB,EAAEqJ,SAAS;MAC3BpJ,cAAc,EAAEoJ;KACjB;IACD,IAAI,CAACW,gBAAgB,EAAE;EACzB;EAEA;EACAC,qBAAqBA,CAAA;IACnB,IAAI,CAACb,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA/J,gBAAgBA,CAAA;IACd,OAAO,CAAC,EAAE,IAAI,CAAClB,aAAa,CAACC,aAAa,IACxC,IAAI,CAACD,aAAa,CAACI,YAAY,IAC/B,IAAI,CAACJ,aAAa,CAACO,WAAW,CAAC;EACnC;EAEAwL,qBAAqBA,CAAA;IACnB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAAChM,aAAa,CAACC,aAAa,EAAE+L,KAAK,EAAE;IAC7C,IAAI,IAAI,CAAChM,aAAa,CAACI,YAAY,EAAE4L,KAAK,EAAE;IAC5C,IAAI,IAAI,CAAChM,aAAa,CAACO,WAAW,EAAEyL,KAAK,EAAE;IAC3C,OAAOA,KAAK;EACd;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACP,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI,CAACD,WAAW,CAACC,kBAAkB;MACvDC,qBAAqB,EAAE,IAAI,CAACrB,gBAAgB,CAAC,CAAC,CAAC;MAC/C1I,gBAAgB,EAAEqJ,SAAS;MAC3BpJ,cAAc,EAAEoJ;KACjB;IACD,IAAI,CAACN,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACnL,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACO,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfsK,cAAc,EAAE;KACjB;IACD,IAAI,CAAC3K,QAAQ,EAAE;EACjB;EAEA6E,cAAcA,CAACmH,MAAc;IAC3B,IAAI,IAAI,CAAClM,aAAa,CAACI,YAAY,KAAK8L,MAAM,EAAE;MAC9C,IAAI,CAAClM,aAAa,CAACI,YAAY,GAAG,EAAE;IACtC,CAAC,MAAM;MACL,IAAI,CAACJ,aAAa,CAACI,YAAY,GAAG8L,MAAM;IAC1C;IACA,IAAI,CAAChM,QAAQ,EAAE;EACjB;EAEAwB,cAAcA,CAAA;IACZ,IAAI,CAACN,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC2C,eAAe,CAACoE,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAClK,QAAQ,GAAG,KAAK,CAAC;EAC/D;EAEAkB,aAAaA,CAACgJ,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QAAE,OAAO,oBAAoB;MAC1C,KAAK,SAAS;QAAE,OAAO,cAAc;MACrC,KAAK,SAAS;QAAE,OAAO,qBAAqB;MAC5C,KAAK,SAAS;QAAE,OAAO,6BAA6B;MACpD,KAAK,UAAU;QAAE,OAAO,YAAY;MACpC;QAAS,OAAO,6BAA6B;IAC/C;EACF;EAEA7H,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACF,UAAU,IAAI,IAAI,CAACA,UAAU,IAAI,CAAC,IAAI,IAAI,CAACA,UAAU,IAAI,IAAI,CAACF,UAAU,EAAE;MACjF,IAAI,CAACR,QAAQ,CAAC,IAAI,CAACU,UAAU,CAAC;IAChC;EACF;EAIAvB,SAASA,CAAC0J,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAACzJ,QAAQ,EAAE;MACjB,IAAI,CAACsG,uBAAuB,GAAG;QAC7B,GAAGmD,IAAI;QACP1K,gBAAgB,EAAE0K,IAAI,CAAC1K,gBAAgB,GAAG,IAAI2K,IAAI,CAACD,IAAI,CAAC1K,gBAAgB,CAAC,GAAGqJ,SAAS;QACrFpJ,cAAc,EAAEyK,IAAI,CAACzK,cAAc,GAAG,IAAI0K,IAAI,CAACD,IAAI,CAACzK,cAAc,CAAC,GAAGoJ;OACvE;MACD,IAAI,CAACnB,aAAa,CAAC0C,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOxO,MAAM,CAACwO,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEAnD,QAAQA,CAAC6C,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAAC5C,KAAK,CAAC6C,aAAa,CAACzL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2I,OAAO,CAAC+C,aAAa,CAAC,IAAI,CAAC9C,KAAK,CAAC6C,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZlK,QAAQ,EAAE,IAAI,CAACsG,uBAAuB,CAACtG,QAAQ;MAC/CjB,gBAAgB,EAAE,IAAI,CAAC6K,UAAU,CAAC,IAAI,CAACtD,uBAAuB,CAACvH,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAAC4K,UAAU,CAAC,IAAI,CAACtD,uBAAuB,CAACtH,cAAc;KAC5E;IAED,IAAI,CAACoI,aAAa,CAAC+C,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAACxB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnD,OAAO,CAACoD,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAzB,gBAAgBA,CAAA;IACd,IAAI,CAAC1B,iBAAiB,CAACoD,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAAC9B,IAAI,CAC7ExN,GAAG,CAACyN,GAAG,IAAG;MACR,MAAMmC,OAAO,GAAGnC,GAAG,CAACoC,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACnM,MAAM,IAAIgK,GAAG,CAAC8B,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChD7O,cAAc,EAAE6O,KAAK,CAAC7O,cAAc;UACpC8O,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAACvD,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIwD,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAACvD,eAAe,CAAC;UAC1F,IAAI,CAACoB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC+B,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAACpC,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAAC+B,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAACvC,WAAW,EAAEC,kBAAkB,EAAEkC,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAAC7B,SAAS,EAAE;EACf;EAEA0C,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAAChC,OAAO,CAACkC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACnC,OAAO,CAAEC,KAAU,IAAI;QACvC,MAAMmC,KAAK,GAAGnC,KAAK,CAACnJ,MAAM;QAC1B,IAAI,CAACmL,SAAS,CAACG,KAAK,CAAC,EAAE;UAAE;UACvBH,SAAS,CAACG,KAAK,CAAC,GAAG,EAAE;QACvB;QACAH,SAAS,CAACG,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBzL,UAAU,EAAEsL,SAAS,CAACtL,UAAU;UAChCC,aAAa,EAAEoJ,KAAK,CAACpJ,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEsJ,KAAK,CAACtJ,QAAQ;UACxBG,MAAM,EAAEmJ,KAAK,CAACnJ,MAAM;UACpBpB,gBAAgB,EAAEuK,KAAK,CAACvK,gBAAgB;UACxCC,cAAc,EAAEsK,KAAK,CAACtK;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC2F,MAAM,CAAC9B,IAAI,CAAC,CAAC8I,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAAClH,MAAM,CAACkG,GAAG,CAAEY,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACjB,GAAG,CAAEU,SAAc,IAAI;QAC5C,MAAMjC,KAAK,GAAGgC,SAAS,CAACG,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAAC/L,UAAU,KAAKsL,SAAS,CAAC;QAC5F,OAAOjC,KAAK,IAAI;UACdrJ,UAAU,EAAEsL,SAAS;UACrBrL,aAAa,EAAE,KAAK;UACpBF,QAAQ,EAAE,IAAI;UACdG,MAAM,EAAEsL,KAAK;UACb1M,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO6M,MAAM;EACf;EAEAI,sBAAsBA,CAACZ,GAAU;IAC/B,MAAMa,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5Cd,GAAG,CAAChC,OAAO,CAACkC,SAAS,IAAG;MACtBa,aAAa,CAACC,GAAG,CAACd,SAAS,CAACtL,UAAU,CAAC;MACvCsL,SAAS,CAACC,OAAO,CAACnC,OAAO,CAAEC,KAAU,IAAI;QACvC4C,SAAS,CAACG,GAAG,CAAC/C,KAAK,CAACnJ,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACwE,MAAM,GAAG2H,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACLzH,MAAM,EAAE2H,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC5D,WAAW,CAAC7J,gBAAgB,IAAI,IAAI,CAAC6J,WAAW,CAAC5J,cAAc,EAAE;MACxE,MAAMsG,SAAS,GAAG,IAAIoE,IAAI,CAAC,IAAI,CAACd,WAAW,CAAC7J,gBAAgB,CAAC;MAC7D,MAAMyG,OAAO,GAAG,IAAIkE,IAAI,CAAC,IAAI,CAACd,WAAW,CAAC5J,cAAc,CAAC;MACzD,IAAIsG,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC0B,OAAO,CAAC+C,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEA;EACAwC,iBAAiBA,CAAA;IACf;IACA,IAAI,CAACC,cAAc,EAAE;IACrB;IACA,IAAI,CAACnC,kBAAkB,EAAE;EAC3B;EAEA;EACAmC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAChF,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC3C,eAAe,GAAG,EAAE;IACzB,IAAI,CAACrD,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC0B,eAAe,GAAG,EAAE;IACzB,IAAI,CAAChF,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAACpB,aAAa,GAAG;MACnBC,aAAa,EAAE,EAAE;MACjBG,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACfsK,cAAc,EAAE;KACjB;IAED;IACA,IAAI,CAACzF,SAAS,GAAG,KAAK;IACtB,IAAI,CAACwF,gBAAgB,GAAG,EAAE;IAE1B;IACA,IAAI,CAAClH,WAAW,GAAG,CAAC;IACpB,IAAI,CAACO,UAAU,GAAG,CAAC;IAEnB;IACA,IAAI,CAAC0G,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC1J,eAAe,GAAG,EAAE;IAEzB;IACA,IAAI,CAACiF,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;EAC5B;EAEAkH,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAAC,IAAI,CAAC3B,WAAW,CAACC,kBAAkB,EAAEkC,GAAG,EAAE;MAC7C,IAAI,CAAC7C,OAAO,GAAG,KAAK;MACpB;IACF;IAEA,IAAI,CAACA,OAAO,GAAG,IAAI;IACnB,IAAI,CAACsE,cAAc,EAAE;IACrB,IAAI,CAACpF,aAAa,CAACyF,mCAAmC,CAAC;MACrDzC,IAAI,EAAE;QACJ0C,YAAY,EAAE,IAAI,CAAClE,WAAW,CAACC,kBAAkB,CAACkC,GAAG;QACrDhM,gBAAgB,EAAE,IAAI,CAAC6J,WAAW,CAAC7J,gBAAgB,GAAG,IAAI,CAAC6K,UAAU,CAAC,IAAI,CAAChB,WAAW,CAAC7J,gBAAgB,CAAC,GAAGqJ,SAAS;QACpHpJ,cAAc,EAAE,IAAI,CAAC4J,WAAW,CAAC5J,cAAc,GAAG,IAAI,CAAC4K,UAAU,CAAC,IAAI,CAAChB,WAAW,CAAC5J,cAAc,CAAC,GAAGoJ;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACL,OAAO,GAAG,KAAK;MACpB,IAAIK,GAAG,CAACoC,OAAO,IAAIpC,GAAG,CAAC8B,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACsC,gBAAgB,GAAGpE,GAAG,CAACoC,OAAO,GAAGpC,GAAG,CAACoC,OAAO,GAAG,EAAE;QACtD,IAAIpC,GAAG,CAACoC,OAAO,EAAE;UACf,IAAI,CAACgC,gBAAgB,GAAG,CAAC,GAAGpE,GAAG,CAACoC,OAAO,CAAC;UACxC,IAAI,CAACsB,sBAAsB,CAAC1D,GAAG,CAACoC,OAAO,CAAC;UACxC,IAAI,CAACiC,mBAAmB,GAAG,IAAI,CAACxB,8BAA8B,CAAC7C,GAAG,CAACoC,OAAO,CAAC;UAC3E;UACA,IAAI,CAACoC,mBAAmB,CAACxE,GAAG,CAACoC,OAAO,CAAC;UACrC;UACA,IAAI,CAACqC,oBAAoB,CAACzE,GAAG,CAACoC,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAoC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC5D,OAAO,CAACkC,SAAS,IAAG;MACvB,MAAM6B,SAAS,GAAG7B,SAAS,CAACtL,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9CsL,SAAS,CAACC,OAAO,EAAEnC,OAAO,CAACC,KAAK,IAAG;QACjC,MAAM+D,YAAY,GAAG/D,KAAK,CAACpJ,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAMuL,KAAK,GAAGnC,KAAK,CAACnJ,MAAM,IAAI,CAAC;QAE/B,IAAI,CAAC+M,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC7B,KAAK,CAAC,EAAE;UACxB+B,QAAQ,CAACD,GAAG,CAAC9B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA+B,QAAQ,CAACC,GAAG,CAAChC,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBzL,UAAU,EAAEmN,SAAS;UAAE;UACvBlN,aAAa,EAAEmN,YAAY;UAAE;UAC7BrN,QAAQ,EAAEsJ,KAAK,CAACtJ,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAEsL,KAAK;UACb1M,gBAAgB,EAAEuK,KAAK,CAACvK,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEsK,KAAK,CAACtK,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACwI,cAAc,GAAG0E,KAAK,CAACC,IAAI,CAACW,WAAW,CAACxC,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAACwC,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAM7I,MAAM,GAAiB2H,KAAK,CAACC,IAAI,CAACiB,QAAQ,CAAC9C,OAAO,EAAE,CAAC,CACxD7H,IAAI,CAAC,CAAC,CAAC8I,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1Bd,GAAG,CAAC,CAAC,CAACpG,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAACvB,IAAI,CAAC,CAAC8I,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAAC1L,UAAU,KAAK2L,CAAC,CAAC3L,UAAU,EAAE;YACjC,OAAO0L,CAAC,CAAC1L,UAAU,CAACyN,aAAa,CAAC9B,CAAC,CAAC3L,UAAU,CAAC;UACjD;UACA,OAAO0L,CAAC,CAACxL,MAAM,GAAGyL,CAAC,CAACzL,MAAM;QAC5B,CAAC,CAAC;QACFf,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLqE,IAAI,EAAE4J,YAAY;QAClB1I,MAAM;QACNvF,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAACyD,IAAI,CAAC,CAAC8I,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClI,IAAI,CAACiK,aAAa,CAAC9B,CAAC,CAACnI,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAACoE,eAAe,GAAG,IAAI,CAACD,cAAc,CAACiD,GAAG,CAAC8C,EAAE,IAAIA,EAAE,CAAClK,IAAI,CAAC;IAC7D,IAAI,CAACmK,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAM1B,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACvE,cAAc,CAACyB,OAAO,CAACwE,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAAC/F,gBAAgB,IAAI+F,QAAQ,CAACpK,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACrE+F,QAAQ,CAAClJ,MAAM,CAAC0E,OAAO,CAACoC,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAAChH,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAACtG,eAAe,GAAGmO,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAACrJ,IAAI,CAAC,CAAC8I,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAmC,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACxP,cAAc,CAAC+K,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAClK,QAAQ,GAAG,KAAK,CAAC;IAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;IACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;IAEtB;IACA,IAAI,CAAC1B,WAAW,GAAG,CAAC;IAEpB;IACA,IAAI,CAAC1D,aAAa,CAACO,WAAW,GAAG,EAAE;IAEnC;IACA,IAAI,CAACmQ,qBAAqB,EAAE;IAE5B;IACA,IAAI,CAAC1Q,aAAa,CAAC6K,cAAc,GAAG,IAAI,CAACD,gBAAgB;IACzD,IAAI,CAAC1K,QAAQ,EAAE;EACjB;EAIA;EACA2Q,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACnG,cAAc,CAACoG,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAAC/F,gBAAgB,IAAI+F,QAAQ,CAACpK,IAAI,KAAK,IAAI,CAACqE,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC5K,aAAa,CAACC,aAAa,EAAE;QACpC,MAAM8Q,OAAO,GAAG,IAAI,CAAC/Q,aAAa,CAACC,aAAa,CAAC+Q,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAAClJ,MAAM,CAACyJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAACrH,MAAM,CAACgK,IAAI,CAAC9E,KAAK,IACrBA,KAAK,CAACrJ,UAAU,CAACiO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChD3E,KAAK,CAACpJ,aAAa,CAACgO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACjR,aAAa,CAACI,YAAY,EAAE;QACnC,MAAMgR,iBAAiB,GAAGT,QAAQ,CAAClJ,MAAM,CAACyJ,IAAI,CAAC3C,KAAK,IAClDA,KAAK,CAACrH,MAAM,CAACgK,IAAI,CAAC9E,KAAK,IAAI,IAAI,CAACiF,mBAAmB,CAACjF,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAACgF,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACpR,aAAa,CAACO,WAAW,EAAE;QAClC,MAAMgH,WAAW,GAAG+J,QAAQ,CAAC,IAAI,CAACtR,aAAa,CAACO,WAAW,CAAC;QAC5D,MAAMgR,gBAAgB,GAAGZ,QAAQ,CAAClJ,MAAM,CAACyJ,IAAI,CAAC3C,KAAK,IACjDA,KAAK,CAAChH,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACgK,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC5D,GAAG,CAACgD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAAC/J,MAAM,GAAGkJ,QAAQ,CAAClJ,MAAM,CAACqJ,MAAM,CAACvC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAACvO,aAAa,CAACO,WAAW,EAAE;UAClC,MAAMgH,WAAW,GAAG+J,QAAQ,CAAC,IAAI,CAACtR,aAAa,CAACO,WAAW,CAAC;UAC5D,IAAIgO,KAAK,CAAChH,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAMkK,cAAc,GAAGlD,KAAK,CAACrH,MAAM,CAACgK,IAAI,CAAC9E,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAACpM,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM8Q,OAAO,GAAG,IAAI,CAAC/Q,aAAa,CAACC,aAAa,CAAC+Q,WAAW,EAAE;YAC9D,IAAI,CAAC5E,KAAK,CAACrJ,UAAU,CAACiO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAACpJ,aAAa,CAACgO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC/Q,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACiR,mBAAmB,CAACjF,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOqF,cAAc;MACvB,CAAC,CAAC,CAAC9D,GAAG,CAACY,KAAK,IAAG;QACb;QACA,MAAMmD,aAAa,GAAG;UAAE,GAAGnD;QAAK,CAAE;QAClCmD,aAAa,CAACxK,MAAM,GAAGqH,KAAK,CAACrH,MAAM,CAAC4J,MAAM,CAAC1E,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAACpM,aAAa,CAACC,aAAa,EAAE;YACpC,MAAM8Q,OAAO,GAAG,IAAI,CAAC/Q,aAAa,CAACC,aAAa,CAAC+Q,WAAW,EAAE;YAC9D,IAAI,CAAC5E,KAAK,CAACrJ,UAAU,CAACiO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAACpJ,aAAa,CAACgO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAC/Q,aAAa,CAACI,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACiR,mBAAmB,CAACjF,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOsF,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAACjF,KAAqB;IAC/C,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAACpM,aAAa,CAACI,YAAY;MACrC,KAAK,QAAQ;QACX,OAAO8L,MAAM,KAAK,QAAQ;MAC5B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,SAAS;QACZ,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;MAAE;IACjB;EACF;EAEA;EACQG,cAAcA,CAACD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAACtJ,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA;IACA,IAAI,CAACsJ,KAAK,CAACvK,gBAAgB,IAAI,CAACuK,KAAK,CAACtK,cAAc,IAClDsK,KAAK,CAACvK,gBAAgB,KAAK,EAAE,IAAIuK,KAAK,CAACtK,cAAc,KAAK,EAAE,EAAE;MAC9D,OAAO,SAAS;IAClB;IAEA,IAAI;MACF;MACA,MAAM6P,GAAG,GAAG,IAAInF,IAAI,EAAE;MACtB,MAAMoF,KAAK,GAAG,IAAIpF,IAAI,CAACmF,GAAG,CAACE,WAAW,EAAE,EAAEF,GAAG,CAACG,QAAQ,EAAE,EAAEH,GAAG,CAACI,OAAO,EAAE,CAAC;MAExE;MACA,IAAI3J,SAAe;MACnB,IAAIgE,KAAK,CAACvK,gBAAgB,CAACsP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxC/I,SAAS,GAAG,IAAIoE,IAAI,CAACJ,KAAK,CAACvK,gBAAgB,CAAC;MAC9C,CAAC,MAAM;QACLuG,SAAS,GAAG,IAAIoE,IAAI,CAACJ,KAAK,CAACvK,gBAAgB,GAAG,WAAW,CAAC;MAC5D;MAEA;MACA,IAAIyG,OAAa;MACjB,IAAI8D,KAAK,CAACtK,cAAc,CAACqP,QAAQ,CAAC,GAAG,CAAC,EAAE;QACtC7I,OAAO,GAAG,IAAIkE,IAAI,CAACJ,KAAK,CAACtK,cAAc,CAAC;MAC1C,CAAC,MAAM;QACLwG,OAAO,GAAG,IAAIkE,IAAI,CAACJ,KAAK,CAACtK,cAAc,GAAG,WAAW,CAAC;MACxD;MAEA;MACA,IAAIkQ,KAAK,CAAC5J,SAAS,CAAC6J,OAAO,EAAE,CAAC,IAAID,KAAK,CAAC1J,OAAO,CAAC2J,OAAO,EAAE,CAAC,EAAE;QAC1DC,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAE;UACnCC,KAAK,EAAEhG,KAAK,CAACvK,gBAAgB;UAC7BwQ,GAAG,EAAEjG,KAAK,CAACtK,cAAc;UACzBwQ,OAAO,EAAElG,KAAK,CAACtJ;SAChB,CAAC;QACF,OAAO,SAAS;MAClB;MAEA;MACA,MAAMyP,aAAa,GAAG,IAAI/F,IAAI,CAACpE,SAAS,CAACyJ,WAAW,EAAE,EAAEzJ,SAAS,CAAC0J,QAAQ,EAAE,EAAE1J,SAAS,CAAC2J,OAAO,EAAE,CAAC;MAClG,MAAMS,WAAW,GAAG,IAAIhG,IAAI,CAAClE,OAAO,CAACuJ,WAAW,EAAE,EAAEvJ,OAAO,CAACwJ,QAAQ,EAAE,EAAExJ,OAAO,CAACyJ,OAAO,EAAE,CAAC;MAE1F;MACA,IAAIH,KAAK,GAAGW,aAAa,EAAE;QACzB,OAAO,SAAS,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIX,KAAK,IAAIW,aAAa,IAAIX,KAAK,IAAIY,WAAW,EAAE;QACzD,OAAO,QAAQ,CAAC,CAAC;MACnB,CAAC,MAAM;QACL,OAAO,SAAS,CAAC,CAAC;MACpB;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,EAAErG,KAAK,CAAC;MACnD,OAAO,SAAS;IAClB;EACF;EAEAxD,OAAOA,CAAC0D,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAAC5C,KAAK,CAACyI,KAAK,EAAE;IAClB,IAAI,CAACzI,KAAK,CAAC0I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACvJ,uBAAuB,CAACvH,gBAAgB,CAAC;IAC9E,IAAI,CAACoI,KAAK,CAAC0I,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACvJ,uBAAuB,CAACtH,cAAc,CAAC;IAC5E,IAAI,CAACmI,KAAK,CAAC2I,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACxJ,uBAAuB,CAACvH,gBAAgB,EAAE,IAAI,CAACuH,uBAAuB,CAACtH,cAAc,CAAC;EACtI;EAEA+Q,gBAAgBA,CAAA;IACd,IAAI,CAACzI,MAAM,CAAC0I,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAACpH,WAAW,EAAEC,kBAAkB,EAAEkC,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACArM,gBAAgBA,CAACmP,QAAwB;IACvC,IAAI,CAACrK,wBAAwB,GAAGqK,QAAQ,IAAI,IAAI;IAEhD;IACA,MAAMoC,iBAAiB,GAAG,IAAI,CAAC3R,cAAc,CAACC,MAAM,GAAG,CAAC;IAExD,IAAI,CAACuG,aAAa,GAAG;MACnBQ,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbT,UAAU,EAAE,CAACkL,iBAAiB,IAAI,CAACpC,QAAQ;MAC3C7F,iBAAiB,EAAE6F,QAAQ,GAAG,CAACA,QAAQ,CAACpK,IAAI,CAAC,GAAG,EAAE;MAClDwE,cAAc,EAAE,EAAE;MAClB3J,cAAc,EAAE;KACjB;IAED;IACA,IAAIuP,QAAQ,EAAE;MACZA,QAAQ,CAAClJ,MAAM,CAAC0E,OAAO,CAACoC,KAAK,IAAG;QAC9BA,KAAK,CAACrM,QAAQ,GAAG,KAAK;QACtBqM,KAAK,CAACrH,MAAM,CAACiF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAClK,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAC6H,aAAa,CAAC0C,IAAI,CAAC,IAAI,CAACuG,kBAAkB,CAAC;EAClD;EAEA;EACA3L,sBAAsBA,CAACkH,KAAiB;IACtC,IAAIA,KAAK,CAACrM,QAAQ,EAAE;MAClBqM,KAAK,CAACrH,MAAM,CAACiF,OAAO,CAACC,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAACtJ,QAAQ,EAAE;UAClBsJ,KAAK,CAAClK,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLqM,KAAK,CAACrH,MAAM,CAACiF,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAClK,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACA4G,aAAaA,CAACwD,GAAQ;IACpB;IACA,IAAI,CAACrC,KAAK,CAACyI,KAAK,EAAE;IAClB,IAAI,CAACzI,KAAK,CAAC0I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC/K,aAAa,CAACQ,SAAS,CAAC;IAC3D,IAAI,CAAC6B,KAAK,CAAC0I,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC/K,aAAa,CAACU,OAAO,CAAC;IACzD,IAAI,CAAC2B,KAAK,CAAC2I,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAChL,aAAa,CAACQ,SAAS,EAAE,IAAI,CAACR,aAAa,CAACU,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC2B,KAAK,CAAC6C,aAAa,CAACzL,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAAC2I,OAAO,CAAC+C,aAAa,CAAC,IAAI,CAAC9C,KAAK,CAAC6C,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMmG,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAACrL,aAAa,CAACC,UAAU,EAAE;MACjC;MACA,IAAI,CAACE,eAAe,CAACoE,OAAO,CAACC,KAAK,IAAG;QACnC,IAAIA,KAAK,CAACtJ,QAAQ,EAAE;UAClBmQ,cAAc,CAACzE,IAAI,CAAC;YAClB1L,QAAQ,EAAEsJ,KAAK,CAACtJ,QAAQ;YACxBjB,gBAAgB,EAAE,IAAI,CAAC6K,UAAU,CAAC,IAAI,CAAC9E,aAAa,CAACQ,SAAS,CAAC;YAC/DtG,cAAc,EAAE,IAAI,CAAC4K,UAAU,CAAC,IAAI,CAAC9E,aAAa,CAACU,OAAO;WAC3D,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAAClH,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;QAClC,IAAI,CAACD,cAAc,CAAC+K,OAAO,CAACC,KAAK,IAAG;UAClC,IAAIA,KAAK,CAACtJ,QAAQ,EAAE;YAClBmQ,cAAc,CAACzE,IAAI,CAAC;cAClB1L,QAAQ,EAAEsJ,KAAK,CAACtJ,QAAQ;cACxBjB,gBAAgB,EAAE,IAAI,CAAC6K,UAAU,CAAC,IAAI,CAAC9E,aAAa,CAACQ,SAAS,CAAC;cAC/DtG,cAAc,EAAE,IAAI,CAAC4K,UAAU,CAAC,IAAI,CAAC9E,aAAa,CAACU,OAAO;aAC3D,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,MAAM,IAAI,IAAI,CAAChC,wBAAwB,EAAE;QACxC;QACA,IAAI,CAACA,wBAAwB,CAACmB,MAAM,CAAC0E,OAAO,CAACoC,KAAK,IAAG;UACnDA,KAAK,CAACrH,MAAM,CAACiF,OAAO,CAACC,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAClK,QAAQ,IAAIkK,KAAK,CAACtJ,QAAQ,EAAE;cACpCmQ,cAAc,CAACzE,IAAI,CAAC;gBAClB1L,QAAQ,EAAEsJ,KAAK,CAACtJ,QAAQ;gBACxBjB,gBAAgB,EAAE,IAAI,CAAC6K,UAAU,CAAC,IAAI,CAAC9E,aAAa,CAACQ,SAAS,CAAC;gBAC/DtG,cAAc,EAAE,IAAI,CAAC4K,UAAU,CAAC,IAAI,CAAC9E,aAAa,CAACU,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAI2K,cAAc,CAAC5R,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC2I,OAAO,CAAC+C,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAAC7C,aAAa,CAAC+C,oCAAoC,CAAC;MACtDC,IAAI,EAAE+F;KACP,CAAC,CAACzH,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAAC8B,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAACnD,OAAO,CAACoD,aAAa,CAAC,QAAQ6F,cAAc,CAAC5R,MAAM,WAAW,CAAC;QACpE;QACA,IAAI,CAACD,cAAc,CAAC+K,OAAO,CAACC,KAAK,IAAIA,KAAK,CAAClK,QAAQ,GAAG,KAAK,CAAC;QAC5D,IAAI,CAACd,cAAc,GAAG,EAAE;QACxB,IAAI,CAACgE,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiI,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACAnK,cAAcA,CAACiJ,KAAqB;IAClC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IACzC,OAAO,UAAUF,MAAM,EAAE;EAC3B;EAEA;EACAgH,eAAeA,CAAC9G,KAAqB;IACnC,IAAIA,KAAK,CAACtJ,QAAQ,EAAE;MAClB;MACA,IAAI,CAACF,SAAS,CAAC,IAAI,CAACuQ,MAAM,EAAE/G,KAAK,CAAC;IACpC;EACF;EAEA;EACA0D,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAAChI,eAAe,GAAG,EAAE;IAEzBgI,IAAI,CAAC5D,OAAO,CAACkC,SAAS,IAAG;MACvB,MAAM6B,SAAS,GAAG7B,SAAS,CAACtL,UAAU,IAAI,EAAE;MAE5CsL,SAAS,CAACC,OAAO,EAAEnC,OAAO,CAACC,KAAK,IAAG;QACjC,IAAI,CAACrE,eAAe,CAACyG,IAAI,CAAC;UACxBzL,UAAU,EAAEmN,SAAS;UACrBlN,aAAa,EAAEoJ,KAAK,CAACpJ,aAAa,IAAI,KAAK;UAC3CF,QAAQ,EAAEsJ,KAAK,CAACtJ,QAAQ,IAAI,CAAC;UAC7BG,MAAM,EAAEmJ,KAAK,CAACnJ,MAAM,IAAI,CAAC;UACzBpB,gBAAgB,EAAEuK,KAAK,CAACvK,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAEsK,KAAK,CAACtK,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAChC,QAAQ,EAAE;IAEf;IACA,IAAI,CAACkT,iBAAiB,EAAE;EAC1B;EAEA;EACQA,iBAAiBA,CAAA;IACvB,MAAMC,YAAY,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE,CAAC;MACV,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE;KACX;IAED,IAAI,CAAC1L,eAAe,CAACoE,OAAO,CAACC,KAAK,IAAG;MACnC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;MACzC,IAAIiH,YAAY,CAACK,cAAc,CAACxH,MAAM,CAAC,EAAE;QACvCmH,YAAY,CAACnH,MAAmC,CAAC,EAAE;MACrD;IACF,CAAC,CAAC;IAEFgG,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAEN,YAAY,CAAC;IAClCnB,OAAO,CAACyB,GAAG,CAAC,OAAO,EAAE,IAAInH,IAAI,EAAE,CAACoH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9D;EAIA;EACA3T,QAAQA,CAAA;IACN;IACA,MAAM4T,qBAAqB,GAAG,IAAI,CAAC1S,cAAc,CAACuM,GAAG,CAACvB,KAAK,IAAIA,KAAK,CAACtJ,QAAQ,CAAC;IAE9E,IAAI,CAAC4B,cAAc,GAAG,IAAI,CAACqD,eAAe,CAAC+I,MAAM,CAAC1E,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAACpM,aAAa,CAACC,aAAa,EAAE;QACpC,MAAM8Q,OAAO,GAAG,IAAI,CAAC/Q,aAAa,CAACC,aAAa,CAAC+Q,WAAW,EAAE;QAC9D,IAAI,CAAC5E,KAAK,CAACrJ,UAAU,CAACiO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAAC3E,KAAK,CAACpJ,aAAa,CAACgO,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACnG,gBAAgB,IAAIwB,KAAK,CAACpJ,aAAa,KAAK,IAAI,CAAC4H,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAAC5K,aAAa,CAACI,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAACiR,mBAAmB,CAACjF,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACpM,aAAa,CAACO,WAAW,EAAE;QAClC,MAAMgH,WAAW,GAAG+J,QAAQ,CAAC,IAAI,CAACtR,aAAa,CAACO,WAAW,CAAC;QAC5D,IAAI6L,KAAK,CAACnJ,MAAM,KAAKsE,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAACnG,cAAc,GAAG,IAAI,CAACsD,cAAc,CAACoM,MAAM,CAAC1E,KAAK,IACpD0H,qBAAqB,CAAC3C,QAAQ,CAAC/E,KAAK,CAACtJ,QAAQ,CAAC,CAC/C;IAED;IACA,IAAI,CAACiF,eAAe,CAACoE,OAAO,CAACC,KAAK,IAAG;MACnCA,KAAK,CAAClK,QAAQ,GAAG,IAAI,CAACd,cAAc,CAAC8P,IAAI,CAAChP,QAAQ,IAAIA,QAAQ,CAACY,QAAQ,KAAKsJ,KAAK,CAACtJ,QAAQ,CAAC;IAC7F,CAAC,CAAC;IAEF;IACA,IAAI,CAACiR,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACrQ,WAAW,GAAG,CAAC;IACpB,IAAI,CAACsQ,gBAAgB,EAAE;EACzB;EAEA;EACQD,oBAAoBA,CAAA;IAC1B,IAAI,IAAI,CAAC3N,eAAe,CAAC/E,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC+D,SAAS,GAAG,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACgB,eAAe,CAAC6N,KAAK,CAAC7H,KAAK,IAC/C,CAACA,KAAK,CAACtJ,QAAQ,IAAIsJ,KAAK,CAAClK,QAAQ,CAClC;IACH;EACF;EAEA;EACA8R,gBAAgBA,CAAA;IACd,IAAI,CAAC/P,UAAU,GAAGO,IAAI,CAAC0P,IAAI,CAAC,IAAI,CAACxP,cAAc,CAACrD,MAAM,GAAG,IAAI,CAACT,QAAQ,CAAC;IACvE,MAAMuT,UAAU,GAAG,CAAC,IAAI,CAACzQ,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC9C,QAAQ;IACzD,MAAMwT,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAACvT,QAAQ;IAC3C,IAAI,CAACwF,eAAe,GAAG,IAAI,CAAC1B,cAAc,CAACiC,KAAK,CAACwN,UAAU,EAAEC,QAAQ,CAAC;IAEtE;IACA,IAAI,CAACL,oBAAoB,EAAE;EAC7B;EAEA;EACAjT,gBAAgBA,CAAA;IACd,IAAI,CAAC4C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACsQ,gBAAgB,EAAE;EACzB;EAEA;EACAvQ,QAAQA,CAAC4Q,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAACpQ,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAG2Q,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACArP,eAAeA,CAAA;IACb,MAAM2P,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAInC,KAAK,GAAG5N,IAAI,CAACgQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC9Q,WAAW,GAAGc,IAAI,CAAC+J,KAAK,CAACgG,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIlC,GAAG,GAAG7N,IAAI,CAACC,GAAG,CAAC,IAAI,CAACR,UAAU,EAAEmO,KAAK,GAAGmC,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIlC,GAAG,GAAGD,KAAK,GAAG,CAAC,GAAGmC,UAAU,EAAE;MAChCnC,KAAK,GAAG5N,IAAI,CAACgQ,GAAG,CAAC,CAAC,EAAEnC,GAAG,GAAGkC,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAIE,CAAC,GAAGrC,KAAK,EAAEqC,CAAC,IAAIpC,GAAG,EAAEoC,CAAC,EAAE,EAAE;MACjCH,KAAK,CAAC9F,IAAI,CAACiG,CAAC,CAAC;IACf;IAEA,OAAOH,KAAK;EACd;EAEA;EACAjP,iBAAiBA,CAAA;IACf,IAAI,CAACe,eAAe,CAAC+F,OAAO,CAACC,KAAK,IAAG;MACnC,IAAIA,KAAK,CAACtJ,QAAQ,EAAE;QAClBsJ,KAAK,CAAClK,QAAQ,GAAG,IAAI,CAACkD,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACsP,oBAAoB,EAAE;EAC7B;EAEA;EACAvS,sBAAsBA,CAAA;IACpB,IAAI,CAACuS,oBAAoB,EAAE;IAC3B,IAAI,CAACtP,SAAS,GAAG,IAAI,CAACgB,eAAe,CAAC6N,KAAK,CAAC7H,KAAK,IAC/C,CAACA,KAAK,CAACtJ,QAAQ,IAAIsJ,KAAK,CAAClK,QAAQ,CAClC;EACH;EAEA;EACAwS,oBAAoBA,CAAA;IAClB,IAAI,CAACtT,cAAc,GAAG,IAAI,CAAC2G,eAAe,CAAC+I,MAAM,CAAC1E,KAAK,IAAIA,KAAK,CAAClK,QAAQ,CAAC;EAC5E;EAEA;EACAyD,IAAIA,CAACgP,KAAa;IAChB,IAAI,IAAI,CAACzO,SAAS,KAAKyO,KAAK,EAAE;MAC5B,IAAI,CAACxO,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGyO,KAAK;MACtB,IAAI,CAACxO,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAACzB,cAAc,CAACiB,IAAI,CAAC,CAAC8I,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAIkG,MAAM,GAAInG,CAAS,CAACkG,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAInG,CAAS,CAACiG,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAACxD,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1ByD,MAAM,GAAGA,MAAM,GAAG,IAAIpI,IAAI,CAACoI,MAAM,CAAC,CAAC3C,OAAO,EAAE,GAAG,CAAC;QAChD4C,MAAM,GAAGA,MAAM,GAAG,IAAIrI,IAAI,CAACqI,MAAM,CAAC,CAAC5C,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAI0C,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAAC5D,WAAW,EAAE;QAC7B6D,MAAM,GAAGA,MAAM,CAAC7D,WAAW,EAAE;MAC/B;MAEA,IAAI4D,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAAC1O,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIyO,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAAC1O,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAAC6N,gBAAgB,EAAE;EACzB;EAEA;EACA3N,cAAcA,CAAC0O,MAAc,EAAE3I,KAAqB;IAClD,OAAOA,KAAK,CAACtJ,QAAQ;EACvB;EAEA;EACAO,aAAaA,CAAC+I,KAAqB;IACjC,MAAMF,MAAM,GAAG,IAAI,CAACG,cAAc,CAACD,KAAK,CAAC;IAEzC,QAAQF,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACA1G,UAAUA,CAAA;IACR;IACA,MAAMwP,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAInJ,IAAI,EAAE,CAACoH,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFwB,IAAI,CAACrX,KAAK,CAAC4X,UAAU,GAAG,QAAQ;IAChCN,QAAQ,CAACpI,IAAI,CAAC2I,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,EAAE;IACZR,QAAQ,CAACpI,IAAI,CAAC6I,WAAW,CAACV,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMe,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAACvR,cAAc,CAACiJ,GAAG,CAACvB,KAAK,IAAI,CAC5CA,KAAK,CAACrJ,UAAU,EAChBqJ,KAAK,CAACpJ,aAAa,EACnB,GAAGoJ,KAAK,CAACnJ,MAAM,GAAG,EAClBmJ,KAAK,CAACvK,gBAAgB,IAAI,KAAK,EAC/BuK,KAAK,CAACtK,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACuB,aAAa,CAAC+I,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAM4I,UAAU,GAAG,CAACgB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClCtI,GAAG,CAACuI,GAAG,IAAIA,GAAG,CAACvI,GAAG,CAACwI,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGpB,UAAU,CAAC,CAAC;EAChC;;;uCA3lCWpL,0BAA0B,EAAArL,EAAA,CAAA8X,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhY,EAAA,CAAA8X,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAlY,EAAA,CAAA8X,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAApY,EAAA,CAAA8X,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAtY,EAAA,CAAA8X,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAxY,EAAA,CAAA8X,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAAzY,EAAA,CAAA8X,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAA3Y,EAAA,CAAA8X,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BxN,0BAA0B;MAAAyN,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAnB1B,EAAE,GAAAjZ,EAAA,CAAAmZ,0BAAA,EAAAnZ,EAAA,CAAAoZ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCrEbjZ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAmB,SAAA,qBAAiC;UACnCnB,EAAA,CAAAG,YAAA,EAAiB;UAQPH,EAPV,CAAAC,cAAA,mBAAc,cAEuB,cAED,cACiC,cACpC,aACK;UAC1BD,EAAA,CAAAmB,SAAA,YAAiC;UAAAnB,EAAA,CAAAE,MAAA,yDACnC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,4FAAc;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACvD;UAEJH,EADF,CAAAC,cAAA,eAA0B,kBAEQ;UAC9BD,EAAA,CAAAmB,SAAA,aAAsC;UAI9CnB,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAMAH,EAHN,CAAAC,cAAA,eAA6B,eACU,eACJ,iBACa;UAAAD,EAAA,CAAAE,MAAA,qBAAG;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;UACvFH,EAAA,CAAAC,cAAA,qBACsD;UADvBD,EAAA,CAAAoB,gBAAA,2BAAAsY,wEAAApY,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA3Z,EAAA,CAAAwB,kBAAA,CAAA0X,GAAA,CAAA/L,WAAA,CAAAC,kBAAA,EAAA9L,MAAA,MAAA4X,GAAA,CAAA/L,WAAA,CAAAC,kBAAA,GAAA9L,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA4C;UACzEtB,EAAA,CAAAW,UAAA,4BAAAiZ,yEAAA;YAAA5Z,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA,OAAA3Z,EAAA,CAAAiB,WAAA,CAAkBiY,GAAA,CAAAlI,iBAAA,EAAmB;UAAA,EAAC;UACtChR,EAAA,CAAAkC,UAAA,KAAA2X,gDAAA,wBAAoE;UAIxE7Z,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpDH,EAAA,CAAAC,cAAA,qBACe;UADeD,EAAA,CAAAoB,gBAAA,2BAAA0Y,wEAAAxY,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA3Z,EAAA,CAAAwB,kBAAA,CAAA0X,GAAA,CAAA7M,gBAAA,EAAA/K,MAAA,MAAA4X,GAAA,CAAA7M,gBAAA,GAAA/K,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA8B;UAACtB,EAAA,CAAAW,UAAA,4BAAAoZ,yEAAA;YAAA/Z,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA,OAAA3Z,EAAA,CAAAiB,WAAA,CAAkBiY,GAAA,CAAA7G,gBAAA,EAAkB;UAAA,EAAC;UAEhGrS,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAkC,UAAA,KAAA8X,gDAAA,wBAAuE;UAI3Eha,EADE,CAAAG,YAAA,EAAY,EACR;UAGJH,EADF,CAAAC,cAAA,eAA+B,iBACa;UAAAD,EAAA,CAAAE,MAAA,4CAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAEtDH,EADF,CAAAC,cAAA,eAA8B,yBACA;UAC1BD,EAAA,CAAAmB,SAAA,mBAAoD;UACpDnB,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAAoB,gBAAA,2BAAA6Y,oEAAA3Y,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA3Z,EAAA,CAAAwB,kBAAA,CAAA0X,GAAA,CAAA/L,WAAA,CAAA7J,gBAAA,EAAAhC,MAAA,MAAA4X,GAAA,CAAA/L,WAAA,CAAA7J,gBAAA,GAAAhC,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAA0C;UAD5CtB,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAmB,SAAA,4BAA8D;UAChEnB,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAA6B;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrCH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAmB,SAAA,mBAAoD;UACpDnB,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAoB,gBAAA,2BAAA8Y,oEAAA5Y,MAAA;YAAAtB,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA3Z,EAAA,CAAAwB,kBAAA,CAAA0X,GAAA,CAAA/L,WAAA,CAAA5J,cAAA,EAAAjC,MAAA,MAAA4X,GAAA,CAAA/L,WAAA,CAAA5J,cAAA,GAAAjC,MAAA;YAAA,OAAAtB,EAAA,CAAAiB,WAAA,CAAAK,MAAA;UAAA,EAAwC;UAD1CtB,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAmB,SAAA,4BAA4D;UAGlEnB,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAgC,eACF,kBAC0D;UAApDD,EAAA,CAAAW,UAAA,mBAAAwZ,6DAAA;YAAAna,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA,OAAA3Z,EAAA,CAAAiB,WAAA,CAASiY,GAAA,CAAApK,kBAAA,EAAoB;UAAA,EAAC;UAC5D9O,EAAA,CAAAmB,SAAA,aAAkC;UAAAnB,EAAA,CAAAE,MAAA,qBACpC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBACiB;UAD8BD,EAAA,CAAAW,UAAA,mBAAAyZ,6DAAA;YAAApa,EAAA,CAAAa,aAAA,CAAA8Y,GAAA;YAAA,OAAA3Z,EAAA,CAAAiB,WAAA,CAASiY,GAAA,CAAAxL,YAAA,EAAc;UAAA,EAAC;UAErE1N,EAAA,CAAAmB,SAAA,aAA2B;UAKrCnB,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UAKNH,EAAA,CAAAkC,UAAA,KAAAmY,0CAAA,oBAAuE;UAkDzEra,EAAA,CAAAG,YAAA,EAAM;UA0RNH,EAvRA,CAAAkC,UAAA,KAAAoY,0CAAA,oBAAyE,KAAAC,0CAAA,kBA2Q2B,KAAAC,0CAAA,kBAYtD;UAalDxa,EADE,CAAAG,YAAA,EAAe,EACP;UAkGVH,EA/FA,CAAAkC,UAAA,KAAAuY,kDAAA,iCAAAza,EAAA,CAAA0a,sBAAA,CAAgE,KAAAC,kDAAA,gCAAA3a,EAAA,CAAA0a,sBAAA,CAkFG,KAAAE,kDAAA,iCAAA5a,EAAA,CAAA0a,sBAAA,CAaf;;;;;UAlfT1a,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAAyC,gBAAA,YAAAyW,GAAA,CAAA/L,WAAA,CAAAC,kBAAA,CAA4C;UAE7CpN,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA8Y,GAAA,CAAA/J,oBAAA,CAAuB;UAQvBnP,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAyC,gBAAA,YAAAyW,GAAA,CAAA7M,gBAAA,CAA8B;UAG1BrM,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAA8Y,GAAA,CAAA9M,eAAA,CAAkB;UAWFpM,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAAya,aAAA,CAA0B;UACtE7a,EAAA,CAAAyC,gBAAA,YAAAyW,GAAA,CAAA/L,WAAA,CAAA7J,gBAAA,CAA0C;UAMEtD,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAA0a,WAAA,CAAwB;UACpE9a,EAAA,CAAAyC,gBAAA,YAAAyW,GAAA,CAAA/L,WAAA,CAAA5J,cAAA,CAAwC;UAQmBvD,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAA8Y,GAAA,CAAAzM,OAAA,CAAoB;UAGXzM,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,aAAA8Y,GAAA,CAAAzM,OAAA,CAAoB;UAY/DzM,EAAA,CAAAM,SAAA,GAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAA8Y,GAAA,CAAA1P,eAAA,CAAA1G,MAAA,KAAgC;UAqDhC9C,EAAA,CAAAM,SAAA,EAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAA8Y,GAAA,CAAA1P,eAAA,CAAA1G,MAAA,KAAgC;UA2QxC9C,EAAA,CAAAM,SAAA,EAAmE;UAAnEN,EAAA,CAAAI,UAAA,SAAA8Y,GAAA,CAAA1P,eAAA,CAAA1G,MAAA,UAAAoW,GAAA,CAAAhI,gBAAA,CAAApO,MAAA,OAAmE;UAYnE9C,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAI,UAAA,SAAA8Y,GAAA,CAAAzM,OAAA,CAAa;;;qBDzV5CrN,YAAY,EAAA2b,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAErb,YAAY,EAAAsb,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAAL,EAAA,CAAAM,OAAA,EAAAxD,EAAA,CAAAyD,eAAA,EAAAzD,EAAA,CAAA0D,mBAAA,EAAA1D,EAAA,CAAA2D,qBAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EAAA5D,EAAA,CAAA6D,mBAAA,EAAA7D,EAAA,CAAA8D,gBAAA,EAAA9D,EAAA,CAAA+D,iBAAA,EAAA/D,EAAA,CAAAgE,iBAAA,EAAAhE,EAAA,CAAAiE,oBAAA,EAAAjE,EAAA,CAAAkE,iBAAA,EAAAlE,EAAA,CAAAmE,eAAA,EAAAnE,EAAA,CAAAoE,qBAAA,EAAApE,EAAA,CAAAqE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAC1Bld,kBAAkB,EAAEC,mBAAmB;MAAAkd,MAAA;MAAAjL,IAAA;QAAAkL,SAAA,EAG7B,CACVld,OAAO,CAAC,YAAY,EAAE,CACpBE,UAAU,CAAC,QAAQ,EAAE,CACnBD,KAAK,CAAC;UAAEkd,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,EACrDjd,OAAO,CAAC,gBAAgB,EAAEF,KAAK,CAAC;UAAEkd,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAe,CAAE,CAAC,CAAC,CAC7E,CAAC,EACFld,UAAU,CAAC,QAAQ,EAAE,CACnBC,OAAO,CAAC,eAAe,EAAEF,KAAK,CAAC;UAAEkd,OAAO,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAmB,CAAE,CAAC,CAAC,CAChF,CAAC,CACH,CAAC;MACH;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}