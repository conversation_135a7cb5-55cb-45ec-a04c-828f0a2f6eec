import { Base64ImagePipe } from './base64-image.pipe';

describe('Base64ImagePipe', () => {
  let pipe: Base64ImagePipe;

  beforeEach(() => {
    pipe = new Base64ImagePipe();
  });

  it('should create an instance', () => {
    expect(pipe).toBeTruthy();
  });

  it('should add data:image/png;base64, prefix to base64 string', () => {
    const base64String = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const result = pipe.transform(base64String);
    expect(result).toBe(`data:image/png;base64,${base64String}`);
  });

  it('should add data:image/jpeg;base64, prefix when imageType is specified', () => {
    const base64String = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const result = pipe.transform(base64String, 'jpeg');
    expect(result).toBe(`data:image/jpeg;base64,${base64String}`);
  });

  it('should return original string if it already has data:image prefix', () => {
    const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const result = pipe.transform(dataUrl);
    expect(result).toBe(dataUrl);
  });

  it('should return original string if it is an HTTP URL', () => {
    const httpUrl = 'http://example.com/image.png';
    const result = pipe.transform(httpUrl);
    expect(result).toBe(httpUrl);
  });

  it('should return original string if it is an HTTPS URL', () => {
    const httpsUrl = 'https://example.com/image.png';
    const result = pipe.transform(httpsUrl);
    expect(result).toBe(httpsUrl);
  });

  it('should return empty string for null or undefined input', () => {
    expect(pipe.transform(null)).toBe('');
    expect(pipe.transform(undefined)).toBe('');
    expect(pipe.transform('')).toBe('');
  });
});
