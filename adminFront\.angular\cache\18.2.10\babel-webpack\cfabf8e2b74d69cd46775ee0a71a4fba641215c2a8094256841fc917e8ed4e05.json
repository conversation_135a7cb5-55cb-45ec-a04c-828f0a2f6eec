{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { HouseholdManagementComponent } from \"./household-management.component\";\nimport { CustomerChangePictureComponent } from \"./customer-change-picture/customer-change-picture.component\";\nimport { SampleSelectionResultComponent } from \"./sample-selection-result/sample-selection-result.component\";\nimport { ModifyFloorPlanComponent } from \"./modify-floor-plan/modify-floor-plan.component\";\nimport { ModifyHouseholdComponent } from \"./modify-household/modify-household.component\";\nimport { ModifyHouseTypeComponent } from \"./modify-house-type/modify-house-type.component\";\nimport { StandardHousePlanComponent } from \"./standard-house-plan/standard-house-plan.component\";\nimport { FinaldochouseManagementComponent } from \"./finaldochouse-management/finaldochouse-management.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HouseholdManagementComponent\n}, {\n  path: \"customer-change-picture/:id1/:id2\",\n  component: CustomerChangePictureComponent\n}, {\n  path: \"sample-selection-result/:id1/:id2\",\n  component: SampleSelectionResultComponent\n}, {\n  path: \"modify-floor-plan/:id\",\n  component: ModifyFloorPlanComponent\n}, {\n  path: \"modify-household/:id\",\n  component: ModifyHouseholdComponent\n}, {\n  path: \"modify-house-type/:id\",\n  component: ModifyHouseTypeComponent\n}, {\n  path: \"standard-house-plan/:id\",\n  component: StandardHousePlanComponent\n}, {\n  path: \"finaldochouse_management/:id1/:id2\",\n  component: FinaldochouseManagementComponent\n}];\nexport let HouseholdRoutingModule = /*#__PURE__*/(() => {\n  class HouseholdRoutingModule {\n    static {\n      this.ɵfac = function HouseholdRoutingModule_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || HouseholdRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: HouseholdRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forChild(routes), RouterModule]\n      });\n    }\n  }\n  return HouseholdRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}