{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { tap } from 'rxjs';\nimport { NbDatepickerModule } from '@nebular/theme';\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\nimport { RadioButtonModule } from 'primeng/radiobutton';\nimport * as moment from 'moment';\nimport { SharedModule } from '../../components/shared.module';\nimport { BaseComponent } from '../../components/base/baseComponent';\nimport { EEvent } from 'src/app/shared/services/event.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/app/shared/services/message.service\";\nimport * as i4 from \"src/app/shared/helper/validationHelper\";\nimport * as i5 from \"src/services/api/services\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/shared/services/event.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"../../components/breadcrumb/breadcrumb.component\";\nconst _c0 = [\"batchSettingDialog\"];\nconst _c1 = [\"dialog\"];\nfunction SettingTimePeriodComponent_nb_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const case_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", case_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", case_r2.CBuildCaseName, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_nb_option_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const building_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", building_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", building_r3, \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_nb_option_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", floor_r6);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", floor_r6, \"F \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_span_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" (\\u5DF2\\u9078 \", ctx_r4.selectedHouses.length, \" \\u7B46) \");\n  }\n}\nfunction SettingTimePeriodComponent_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"div\", 47)(2, \"div\", 48)(3, \"nb-form-field\");\n    i0.ɵɵelement(4, \"nb-icon\", 49);\n    i0.ɵɵelementStart(5, \"input\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.searchKeyword, $event) || (ctx_r4.filterOptions.searchKeyword = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_input_ngModelChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 51)(7, \"nb-select\", 52);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.statusFilter, $event) || (ctx_r4.filterOptions.statusFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_7_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(8, \"nb-option\", 20);\n    i0.ɵɵtext(9, \"\\u5168\\u90E8\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"nb-option\", 53);\n    i0.ɵɵtext(11, \"\\u9032\\u884C\\u4E2D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"nb-option\", 54);\n    i0.ɵɵtext(13, \"\\u5F85\\u958B\\u653E\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"nb-option\", 55);\n    i0.ɵɵtext(15, \"\\u5DF2\\u904E\\u671F\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"nb-option\", 56);\n    i0.ɵɵtext(17, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"nb-option\", 57);\n    i0.ɵɵtext(19, \"\\u5DF2\\u505C\\u7528\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 51)(21, \"nb-select\", 58);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.filterOptions.floorFilter, $event) || (ctx_r4.filterOptions.floorFilter = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_21_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSearch());\n    });\n    i0.ɵɵelementStart(22, \"nb-option\", 20);\n    i0.ɵɵtext(23, \"\\u5168\\u90E8\\u6A13\\u5C64\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, SettingTimePeriodComponent_div_57_nb_option_24_Template, 2, 2, \"nb-option\", 17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 51)(26, \"nb-select\", 59);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.pageSize, $event) || (ctx_r4.pageSize = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_26_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onPageSizeChange());\n    });\n    i0.ɵɵelementStart(27, \"nb-option\", 45);\n    i0.ɵɵtext(28, \"50\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"nb-option\", 45);\n    i0.ɵɵtext(30, \"100\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"nb-option\", 45);\n    i0.ɵɵtext(32, \"200\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"nb-option\", 45);\n    i0.ɵɵtext(34, \"500\\u7B46/\\u9801\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 48)(36, \"div\", 60)(37, \"div\", 61);\n    i0.ɵɵtext(38);\n    i0.ɵɵtemplate(39, SettingTimePeriodComponent_div_57_span_39_Template, 2, 1, \"span\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 63)(41, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_41_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setViewMode(\"table\"));\n    });\n    i0.ɵɵelement(42, \"i\", 65);\n    i0.ɵɵtext(43, \" \\u8868\\u683C \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_57_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setViewMode(\"card\"));\n    });\n    i0.ɵɵelement(45, \"i\", 67);\n    i0.ɵɵtext(46, \" \\u5361\\u7247 \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.searchKeyword);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.statusFilter);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.filterOptions.floorFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.availableFloors);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 50);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 100);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 200);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 500);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46\\u8CC7\\u6599 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.selectedHouses.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.viewMode === \"table\")(\"btn-outline-primary\", ctx_r4.viewMode !== \"table\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"btn-primary\", ctx_r4.viewMode === \"card\")(\"btn-outline-primary\", ctx_r4.viewMode !== \"card\");\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeStartDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r9.CChangeEndDate, \"yyyy-MM-dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_58_tr_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"nb-checkbox\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_tr_40_Template_nb_checkbox_ngModelChange_2_listener($event) {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r9.selected, $event) || (house_r9.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_tr_40_Template_nb_checkbox_ngModelChange_2_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtemplate(10, SettingTimePeriodComponent_div_58_tr_40_span_10_Template, 3, 4, \"span\", 91)(11, SettingTimePeriodComponent_div_58_tr_40_span_11_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtemplate(13, SettingTimePeriodComponent_div_58_tr_40_span_13_Template, 3, 4, \"span\", 91)(14, SettingTimePeriodComponent_div_58_tr_40_span_14_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\")(16, \"span\", 93);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_tr_40_Template_button_click_19_listener() {\n      const house_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r10 = i0.ɵɵreference(67);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r10, house_r9));\n    });\n    i0.ɵɵelement(20, \"i\", 74);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const house_r9 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-row-selected\", house_r9.selected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r9.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(house_r9.CBuildingName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", house_r9.CFloor, \"F\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeStartDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", house_r9.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r9.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r9));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStatusText(house_r9), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !house_r9.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_div_41_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 99)(1, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_li_9_Template_button_click_1_listener() {\n      const page_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r13));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r13 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r13 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r13);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"nav\")(2, \"ul\", 98)(3, \"li\", 99)(4, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 99)(7, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_58_div_41_li_9_Template, 3, 3, \"li\", 101);\n    i0.ɵɵelementStart(10, \"li\", 99)(11, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 99)(14, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_div_41_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 60)(3, \"div\", 71)(4, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_4_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵtext(5, \" \\u5168\\u9078 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.openBatchSetting());\n    });\n    i0.ɵɵelement(7, \"i\", 74);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.exportData());\n    });\n    i0.ɵɵelement(10, \"i\", 76);\n    i0.ɵɵtext(11, \" \\u532F\\u51FA \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 77);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 78)(15, \"table\", 79)(16, \"thead\", 80)(17, \"tr\")(18, \"th\", 81)(19, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectAll, $event) || (ctx_r4.selectAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_19_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSelectAllChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"th\", 82);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_20_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CHouseHold\"));\n    });\n    i0.ɵɵtext(21, \" \\u6236\\u578B \");\n    i0.ɵɵelement(22, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 82);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_23_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CBuildingName\"));\n    });\n    i0.ɵɵtext(24, \" \\u68DF\\u5225 \");\n    i0.ɵɵelement(25, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"th\", 84);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_26_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CFloor\"));\n    });\n    i0.ɵɵtext(27, \" \\u6A13\\u5C64 \");\n    i0.ɵɵelement(28, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"th\", 85);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_29_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeStartDate\"));\n    });\n    i0.ɵɵtext(30, \" \\u958B\\u59CB\\u65E5\\u671F \");\n    i0.ɵɵelement(31, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"th\", 85);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_58_Template_th_click_32_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.sort(\"CChangeEndDate\"));\n    });\n    i0.ɵɵtext(33, \" \\u7D50\\u675F\\u65E5\\u671F \");\n    i0.ɵɵelement(34, \"i\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"th\", 86);\n    i0.ɵɵtext(36, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"th\", 87);\n    i0.ɵɵtext(38, \"\\u64CD\\u4F5C\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"tbody\");\n    i0.ɵɵtemplate(40, SettingTimePeriodComponent_div_58_tr_40_Template, 21, 15, \"tr\", 88);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(41, SettingTimePeriodComponent_div_58_div_41_Template, 16, 13, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.selectedHouses.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u6279\\u6B21\\u8A2D\\u5B9A (\", ctx_r4.selectedHouses.length, \") \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.filteredHouses.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate3(\" \\u986F\\u793A \", (ctx_r4.currentPage - 1) * ctx_r4.pageSize + 1, \" - \", ctx_r4.Math.min(ctx_r4.currentPage * ctx_r4.pageSize, ctx_r4.filteredHouses.length), \" / \\u5171 \", ctx_r4.filteredHouses.length, \" \\u7B46 \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectAll);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CHouseHold\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CBuildingName\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CFloor\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeStartDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-sort-up\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"asc\")(\"fa-sort-down\", ctx_r4.sortField === \"CChangeEndDate\" && ctx_r4.sortDirection === \"desc\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r15.CChangeStartDate, \"MM/dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 116);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r15 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 1, house_r15.CChangeEndDate, \"MM/dd\"), \" \");\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 96);\n    i0.ɵɵtext(1, \"\\u672A\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"nb-card\", 107)(2, \"nb-card-body\", 108)(3, \"div\", 109)(4, \"div\", 110)(5, \"h6\", 111);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\", 96);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"nb-checkbox\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_div_59_div_2_Template_nb_checkbox_ngModelChange_9_listener($event) {\n      const house_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r15.selected, $event) || (house_r15.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_div_59_div_2_Template_nb_checkbox_ngModelChange_9_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.onHouseSelectionChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 112)(11, \"div\", 113)(12, \"small\", 96);\n    i0.ɵɵtext(13, \"\\u958B\\u59CB\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, SettingTimePeriodComponent_div_59_div_2_span_14_Template, 3, 4, \"span\", 114)(15, SettingTimePeriodComponent_div_59_div_2_span_15_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 113)(17, \"small\", 96);\n    i0.ɵɵtext(18, \"\\u7D50\\u675F\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, SettingTimePeriodComponent_div_59_div_2_span_19_Template, 3, 4, \"span\", 114)(20, SettingTimePeriodComponent_div_59_div_2_span_20_Template, 2, 0, \"span\", 92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 60)(22, \"span\", 115);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 94);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_2_Template_button_click_24_listener() {\n      const house_r15 = i0.ɵɵrestoreView(_r14).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      const dialog_r10 = i0.ɵɵreference(67);\n      return i0.ɵɵresetView(ctx_r4.openModel(dialog_r10, house_r15));\n    });\n    i0.ɵɵelement(25, \"i\", 74);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const house_r15 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"selected\", house_r15.selected)(\"disabled\", !house_r15.CHouseId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(house_r15.CHouseHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", house_r15.CBuildingName, \" - \", house_r15.CFloor, \"F\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r15.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r15.CHouseId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", house_r15.CChangeStartDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r15.CChangeStartDate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", house_r15.CChangeEndDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !house_r15.CChangeEndDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r4.getStatusClass(house_r15));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getStatusText(house_r15), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !house_r15.CHouseId);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_3_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 99)(1, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_li_9_Template_button_click_1_listener() {\n      const page_r18 = i0.ɵɵrestoreView(_r17).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.goToPage(page_r18));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r18 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"active\", page_r18 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(page_r18);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 97)(1, \"nav\")(2, \"ul\", 98)(3, \"li\", 99)(4, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(1));\n    });\n    i0.ɵɵtext(5, \"\\u9996\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"li\", 99)(7, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage - 1));\n    });\n    i0.ɵɵtext(8, \"\\u4E0A\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, SettingTimePeriodComponent_div_59_div_3_li_9_Template, 3, 3, \"li\", 101);\n    i0.ɵɵelementStart(10, \"li\", 99)(11, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.currentPage + 1));\n    });\n    i0.ɵɵtext(12, \"\\u4E0B\\u4E00\\u9801\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 99)(14, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_div_59_div_3_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.goToPage(ctx_r4.totalPages));\n    });\n    i0.ɵɵtext(15, \"\\u672B\\u9801\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.getVisiblePages());\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages);\n  }\n}\nfunction SettingTimePeriodComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103)(1, \"div\", 104);\n    i0.ɵɵtemplate(2, SettingTimePeriodComponent_div_59_div_2_Template, 26, 17, \"div\", 105);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_div_59_div_3_Template, 16, 13, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.paginatedHouses)(\"ngForTrackBy\", ctx_r4.trackByHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.totalPages > 1);\n  }\n}\nfunction SettingTimePeriodComponent_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 96);\n    i0.ɵɵelement(4, \"i\", 118);\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"\\u8ACB\\u9078\\u64C7\\u5EFA\\u6848\\u5F8C\\u67E5\\u8A62\\u8CC7\\u6599\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"nb-card\")(2, \"nb-card-body\")(3, \"div\", 96)(4, \"div\", 119)(5, \"span\", 120);\n    i0.ɵɵtext(6, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 121);\n    i0.ɵɵtext(8, \"\\u8CC7\\u6599\\u8F09\\u5165\\u4E2D\\uFF0C\\u8ACB\\u7A0D\\u5019...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_nb_checkbox_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-checkbox\", 90);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener($event) {\n      const house_r23 = i0.ɵɵrestoreView(_r22).$implicit;\n      i0.ɵɵtwoWayBindingSet(house_r23.selected, $event) || (house_r23.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const house_r23 = ctx.$implicit;\n    i0.ɵɵtwoWayProperty(\"ngModel\", house_r23.selected);\n    i0.ɵɵproperty(\"disabled\", !house_r23.CHouseId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", house_r23.CHouseHold, \" (\", house_r23.CBuildingName, \") \");\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 137);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_nb_checkbox_1_Template, 2, 4, \"nb-checkbox\", 138);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", floor_r21.houses);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_div_28_div_1_Template_nb_checkbox_ngModelChange_1_listener($event) {\n      const floor_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      i0.ɵɵtwoWayBindingSet(floor_r21.selected, $event) || (floor_r21.selected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_div_28_div_1_Template_nb_checkbox_ngModelChange_1_listener() {\n      const floor_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.onFloorSelectionChange(floor_r21));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_Template, 2, 1, \"div\", 136);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const floor_r21 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", floor_r21.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", floor_r21.floorNumber, \"F (\", floor_r21.houses.length, \" \\u6236) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", floor_r21.selected);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 121);\n    i0.ɵɵtemplate(1, SettingTimePeriodComponent_ng_template_62_div_28_div_1_Template, 4, 4, \"div\", 134);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.selectedBuildingForBatch.floors);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 122)(1, \"nb-card-header\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 123)(5, \"label\");\n    i0.ɵɵtext(6, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16 \");\n    i0.ɵɵelementStart(7, \"span\", 124);\n    i0.ɵɵtext(8, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 125)(10, \"nb-form-field\", 126);\n    i0.ɵɵelement(11, \"nb-icon\", 24);\n    i0.ɵɵelementStart(12, \"input\", 127);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_Template_input_ngModelChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.startDate, $event) || (ctx_r4.batchSettings.startDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"nb-datepicker\", 26, 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 128);\n    i0.ɵɵtext(16, \"~\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"nb-form-field\", 126);\n    i0.ɵɵelement(18, \"nb-icon\", 24);\n    i0.ɵɵelementStart(19, \"input\", 127);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_Template_input_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.endDate, $event) || (ctx_r4.batchSettings.endDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"nb-datepicker\", 26, 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 123)(23, \"label\");\n    i0.ɵɵtext(24, \"\\u9069\\u7528\\u7BC4\\u570D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 129)(26, \"nb-checkbox\", 72);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_62_Template_nb_checkbox_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.batchSettings.applyToAll, $event) || (ctx_r4.batchSettings.applyToAll = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵtext(27, \" \\u5168\\u90E8\\u6236\\u5225 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, SettingTimePeriodComponent_ng_template_62_div_28_Template, 2, 1, \"div\", 130);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"nb-card-footer\", 131)(30, \"button\", 132);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_62_Template_button_click_30_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r24));\n    });\n    i0.ɵɵtext(31, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_62_Template_button_click_32_listener() {\n      const ref_r24 = i0.ɵɵrestoreView(_r19).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onBatchSubmit(ref_r24));\n    });\n    i0.ɵɵtext(33, \"\\u6279\\u6B21\\u8A2D\\u5B9A\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const batchStartDate_r25 = i0.ɵɵreference(14);\n    const batchEndDate_r26 = i0.ɵɵreference(21);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \\u6279\\u6B21\\u8A2D\\u5B9A - \", (ctx_r4.selectedBuildingForBatch == null ? null : ctx_r4.selectedBuildingForBatch.name) || \"\\u5168\\u90E8\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", batchStartDate_r25);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.startDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", batchEndDate_r26);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.endDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.batchSettings.applyToAll);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.batchSettings.applyToAll && ctx_r4.selectedBuildingForBatch);\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"nb-card\", 139);\n    i0.ɵɵelement(1, \"nb-card-header\")(2, \"nb-card-body\")(3, \"nb-card-footer\", 140);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SettingTimePeriodComponent_ng_template_66_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 139)(1, \"nb-card-header\");\n    i0.ɵɵtext(2, \" \\u60A8\\u6B63\\u5728\\u7DE8\\u8F2F \");\n    i0.ɵɵelementStart(3, \"span\", 141);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u7684\\u9078\\u6A23\\u958B\\u653E\\u6642\\u6BB5 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"nb-card-body\", 142)(7, \"div\", 21)(8, \"label\", 22);\n    i0.ɵɵtext(9, \"\\u958B\\u653E\\u6642\\u9593\\u8D77\\u8A16\");\n    i0.ɵɵelementStart(10, \"span\", 124);\n    i0.ɵɵtext(11, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"nb-form-field\", 143);\n    i0.ɵɵelement(13, \"nb-icon\", 24);\n    i0.ɵɵelementStart(14, \"input\", 144);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_66_Template_input_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeStartDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeStartDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"nb-datepicker\", 26, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 145);\n    i0.ɵɵtext(18, \" ~ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"nb-form-field\");\n    i0.ɵɵelement(20, \"nb-icon\", 24);\n    i0.ɵɵelementStart(21, \"input\", 146);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_ng_template_66_Template_input_ngModelChange_21_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r4 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r4.selectedHouseChangeDate.CChangeEndDate, $event) || (ctx_r4.selectedHouseChangeDate.CChangeEndDate = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"nb-datepicker\", 26, 8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"nb-card-footer\", 140)(25, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_66_Template_button_click_25_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r27).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onClose(ref_r28));\n    });\n    i0.ɵɵtext(26, \"\\u53D6\\u6D88\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 147);\n    i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_ng_template_66_Template_button_click_27_listener() {\n      const ref_r28 = i0.ɵɵrestoreView(_r27).dialogRef;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onSubmit(ref_r28));\n    });\n    i0.ɵɵtext(28, \"\\u5132\\u5B58\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const changeStartDate_r29 = i0.ɵɵreference(16);\n    const changeEndDate_r30 = i0.ɵɵreference(23);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.selectedHouseChangeDate.CHouseHold, \" - \", ctx_r4.selectedHouseChangeDate.CFloor, \"F\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"nbDatepicker\", changeStartDate_r29);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeStartDate);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"nbDatepicker\", changeEndDate_r30);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r4.selectedHouseChangeDate.CChangeEndDate);\n  }\n}\nexport class SettingTimePeriodComponent extends BaseComponent {\n  constructor(_allow, dialogService, message, valid, _houseService, _buildCaseService, router, _eventService) {\n    super(_allow);\n    this._allow = _allow;\n    this.dialogService = dialogService;\n    this.message = message;\n    this.valid = valid;\n    this._houseService = _houseService;\n    this._buildCaseService = _buildCaseService;\n    this.router = router;\n    this._eventService = _eventService;\n    this.tempBuildCaseId = -1;\n    this.buildCaseOptions = [{\n      label: '全部',\n      value: ''\n    }];\n    this.isStatus = true;\n    // 新增的屬性\n    this.buildingGroups = [];\n    this.buildingOptions = [];\n    this.selectedBuilding = '';\n    this.availableFloors = [];\n    // 篩選和搜尋\n    this.filterOptions = {\n      searchKeyword: '',\n      statusFilter: '',\n      floorFilter: '',\n      buildingFilter: ''\n    };\n    // 批次設定\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: true,\n      selectedBuildings: [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    this.selectedBuildingForBatch = null;\n    // 新增：表格視圖相關屬性\n    this.viewMode = 'table';\n    this.flattenedHouses = [];\n    this.filteredHouses = [];\n    this.paginatedHouses = [];\n    this.selectedHouses = [];\n    this.selectAll = false;\n    this.loading = false;\n    // 分頁相關\n    this.currentPage = 1;\n    this.pageSize = 100;\n    this.totalPages = 1;\n    // 排序相關\n    this.sortField = '';\n    this.sortDirection = 'asc';\n    // 數學函數引用\n    this.Math = Math;\n    this.selectedHouseChangeDate = {\n      CChangeStartDate: '',\n      CChangeEndDate: '',\n      CFloor: undefined,\n      CHouseHold: '',\n      CHouseId: undefined\n    };\n    this._eventService.receive().pipe(tap(res => {\n      if (res.action === \"GET_BUILDCASE\" /* EEvent.GET_BUILDCASE */) {\n        this.tempBuildCaseId = res.payload;\n      }\n    })).subscribe();\n  }\n  ngOnInit() {\n    this.searchQuery = {\n      CBuildCaseSelected: null,\n      CBuildingNameSelected: this.buildCaseOptions[0],\n      CChangeStartDate: undefined,\n      CChangeEndDate: undefined\n    };\n    this.getUserBuildCase();\n  }\n  openModel(ref, item) {\n    if (item.CHouseId) {\n      this.selectedHouseChangeDate = {\n        ...item,\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined\n      };\n      this.dialogService.open(ref);\n    }\n  }\n  formatDate(CChangeDate) {\n    if (CChangeDate) {\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss');\n    }\n    return '';\n  }\n  onSubmit(ref) {\n    this.validation();\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    const param = {\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate)\n    };\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: [param]\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(\"執行成功\");\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  getUserBuildCase() {\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({\n      body: {}\n    }).pipe(tap(res => {\n      const entries = res.Entries ?? []; // entries not undefined and not null\n      if (entries.length && res.StatusCode === 0) {\n        this.userBuildCaseOptions = entries.map(entry => ({\n          CBuildCaseName: entry.CBuildCaseName,\n          cID: entry.cID\n        }));\n        if (this.tempBuildCaseId != -1) {\n          let index = this.userBuildCaseOptions.findIndex(x => x.cID == this.tempBuildCaseId);\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\n        } else {\n          this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\n        }\n        const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\n        if (selectedCID) {\n          this.getHouseChangeDate();\n        }\n      }\n    })).subscribe();\n  }\n  convertHouseholdArrayOptimized(arr) {\n    const floorDict = {}; // Initialize dictionary to group elements by CFloor\n    arr.forEach(household => {\n      household.CHouses.forEach(house => {\n        const floor = house.CFloor;\n        if (!floorDict[floor]) {\n          // If CFloor is not in the dictionary, initialize an empty list\n          floorDict[floor] = [];\n        }\n        floorDict[floor].push({\n          CHouseHold: household.CHouseHold,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId,\n          CFloor: house.CFloor,\n          CChangeStartDate: house.CChangeStartDate,\n          CChangeEndDate: house.CChangeEndDate\n        });\n      });\n    });\n    // Arrange floors in ascending order\n    this.floors.sort((a, b) => b - a);\n    const result = this.floors.map(floor => {\n      return this.households.map(household => {\n        const house = floorDict[floor].find(h => h.CHouseHold === household);\n        return house || {\n          CHouseHold: household,\n          CBuildingName: '未分類',\n          CHouseId: null,\n          CFloor: floor,\n          CChangeStartDate: null,\n          CChangeEndDate: null\n        };\n      });\n    });\n    return result;\n  }\n  getFloorsAndHouseholds(arr) {\n    const floorsSet = new Set();\n    const householdsSet = new Set();\n    arr.forEach(household => {\n      householdsSet.add(household.CHouseHold);\n      household.CHouses.forEach(house => {\n        floorsSet.add(house.CFloor);\n      });\n    });\n    this.floors = Array.from(floorsSet);\n    this.households = Array.from(householdsSet);\n    return {\n      floors: Array.from(floorsSet),\n      households: Array.from(householdsSet)\n    };\n  }\n  validationDate() {\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\n      if (startDate && endDate && startDate > endDate) {\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`]);\n      }\n    }\n  }\n  getHouseChangeDate() {\n    this.loading = true;\n    this.validationDate();\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\n      body: {\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined\n      }\n    }).subscribe(res => {\n      this.loading = false;\n      if (res.Entries && res.StatusCode == 0) {\n        this.houseChangeDates = res.Entries ? res.Entries : [];\n        if (res.Entries) {\n          this.houseChangeDates = [...res.Entries];\n          this.getFloorsAndHouseholds(res.Entries);\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries);\n          // 新增：建立棟別分組資料\n          this.buildBuildingGroups(res.Entries);\n          // 新增：建立扁平化資料\n          this.buildFlattenedHouses(res.Entries);\n        }\n      }\n    });\n  }\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\n  buildBuildingGroups(data) {\n    const buildingMap = new Map();\n    data.forEach(household => {\n      const houseType = household.CHouseHold || ''; // 戶型\n      household.CHouses?.forEach(house => {\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\n        const floor = house.CFloor || 0;\n        if (!buildingMap.has(buildingName)) {\n          buildingMap.set(buildingName, new Map());\n        }\n        const floorMap = buildingMap.get(buildingName);\n        if (!floorMap.has(floor)) {\n          floorMap.set(floor, []);\n        }\n        floorMap.get(floor).push({\n          CHouseHold: houseType,\n          // 戶型\n          CBuildingName: buildingName,\n          // 棟別\n          CHouseId: house.CHouseId || 0,\n          CFloor: floor,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 轉換為BuildingGroup格式\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\n      const floors = Array.from(floorMap.entries()).sort(([a], [b]) => b - a) // 樓層由高到低排序\n      .map(([floorNumber, houses]) => ({\n        floorNumber,\n        houses: houses.sort((a, b) => {\n          // 排序邏輯：先按戶型排序，再按樓層排序\n          if (a.CHouseHold !== b.CHouseHold) {\n            return a.CHouseHold.localeCompare(b.CHouseHold);\n          }\n          return a.CFloor - b.CFloor;\n        }),\n        selected: false\n      }));\n      return {\n        name: buildingName,\n        floors,\n        selected: false\n      };\n    }).sort((a, b) => a.name.localeCompare(b.name));\n    // 更新棟別選項和可用樓層\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\n    this.updateAvailableFloors();\n  }\n  // 新增：更新可用樓層\n  updateAvailableFloors() {\n    const floorsSet = new Set();\n    this.buildingGroups.forEach(building => {\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\n        building.floors.forEach(floor => {\n          floorsSet.add(floor.floorNumber);\n        });\n      }\n    });\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\n  }\n  // 新增：棟別選擇變更處理\n  onBuildingChange() {\n    this.updateAvailableFloors();\n    this.filterOptions.buildingFilter = this.selectedBuilding;\n  }\n  // 新增：取得過濾後的棟別資料\n  getFilteredBuildings() {\n    return this.buildingGroups.filter(building => {\n      // 棟別篩選\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\n        return false;\n      }\n      // 關鍵字搜尋 (搜尋戶型)\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        const hasMatchingHouse = building.floors.some(floor => floor.houses.some(house => house.CHouseHold.toLowerCase().includes(keyword) || house.CBuildingName.toLowerCase().includes(keyword)));\n        if (!hasMatchingHouse) {\n          return false;\n        }\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        const hasMatchingStatus = building.floors.some(floor => floor.houses.some(house => this.matchesStatusFilter(house)));\n        if (!hasMatchingStatus) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        const hasMatchingFloor = building.floors.some(floor => floor.floorNumber === floorNumber);\n        if (!hasMatchingFloor) {\n          return false;\n        }\n      }\n      return true;\n    }).map(building => {\n      // 對每個棟別，也要篩選其樓層和戶別\n      const filteredBuilding = {\n        ...building\n      };\n      filteredBuilding.floors = building.floors.filter(floor => {\n        // 樓層篩選\n        if (this.filterOptions.floorFilter) {\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\n          if (floor.floorNumber !== floorNumber) {\n            return false;\n          }\n        }\n        // 檢查該樓層是否有符合條件的戶別\n        const hasValidHouses = floor.houses.some(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return hasValidHouses;\n      }).map(floor => {\n        // 篩選戶別\n        const filteredFloor = {\n          ...floor\n        };\n        filteredFloor.houses = floor.houses.filter(house => {\n          // 關鍵字篩選 (搜尋戶型或棟別)\n          if (this.filterOptions.searchKeyword) {\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\n            if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n              return false;\n            }\n          }\n          // 狀態篩選\n          if (this.filterOptions.statusFilter) {\n            if (!this.matchesStatusFilter(house)) {\n              return false;\n            }\n          }\n          return true;\n        });\n        return filteredFloor;\n      });\n      return filteredBuilding;\n    });\n  }\n  // 新增：檢查戶別是否符合狀態篩選\n  matchesStatusFilter(house) {\n    const status = this.getHouseStatus(house);\n    switch (this.filterOptions.statusFilter) {\n      case 'active':\n        return status === 'active';\n      case 'inactive':\n        return status === 'not-set';\n      case 'disabled':\n        return status === 'disabled';\n      default:\n        return true;\n    }\n  }\n  // 新增：取得戶別狀態\n  getHouseStatus(house) {\n    if (!house.CHouseId) {\n      return 'disabled';\n    }\n    if (house.CChangeStartDate && house.CChangeEndDate) {\n      const now = new Date();\n      const startDate = new Date(house.CChangeStartDate);\n      const endDate = new Date(house.CChangeEndDate);\n      if (now < startDate) {\n        return 'pending';\n      } else if (now >= startDate && now <= endDate) {\n        return 'active';\n      } else {\n        return 'expired';\n      }\n    }\n    return 'not-set';\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  validation() {\n    this.valid.clear();\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate);\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate);\n  }\n  onNavigateWithId() {\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`]);\n  }\n  // 新增：開啟批次設定對話框\n  openBatchSetting(building) {\n    this.selectedBuildingForBatch = building || null;\n    this.batchSettings = {\n      startDate: null,\n      endDate: null,\n      applyToAll: !building,\n      selectedBuildings: building ? [building.name] : [],\n      selectedFloors: [],\n      selectedHouses: []\n    };\n    // 重置選擇狀態\n    if (building) {\n      building.floors.forEach(floor => {\n        floor.selected = false;\n        floor.houses.forEach(house => house.selected = false);\n      });\n    }\n    // 開啟對話框\n    this.dialogService.open(this.batchSettingDialog);\n  }\n  // 新增：樓層選擇變更處理\n  onFloorSelectionChange(floor) {\n    if (floor.selected) {\n      floor.houses.forEach(house => {\n        if (house.CHouseId) {\n          house.selected = true;\n        }\n      });\n    } else {\n      floor.houses.forEach(house => house.selected = false);\n    }\n  }\n  // 新增：批次提交\n  onBatchSubmit(ref) {\n    // 驗證批次設定\n    this.valid.clear();\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\n    if (this.valid.errorMessages.length > 0) {\n      this.message.showErrorMSGs(this.valid.errorMessages);\n      return;\n    }\n    // 收集要更新的房屋\n    const housesToUpdate = [];\n    if (this.batchSettings.applyToAll) {\n      // 全部戶別\n      this.buildingGroups.forEach(building => {\n        building.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      });\n    } else {\n      // 選擇的戶別\n      if (this.selectedBuildingForBatch) {\n        this.selectedBuildingForBatch.floors.forEach(floor => {\n          floor.houses.forEach(house => {\n            if (house.selected && house.CHouseId) {\n              housesToUpdate.push({\n                CHouseId: house.CHouseId,\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\n              });\n            }\n          });\n        });\n      }\n    }\n    if (housesToUpdate.length === 0) {\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\n      return;\n    }\n    // 調用API進行批次更新\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\n      body: housesToUpdate\n    }).subscribe(res => {\n      if (res.StatusCode == 0) {\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\n        this.getHouseChangeDate();\n        ref.close();\n      }\n    });\n  }\n  // 新增：取得狀態樣式類別\n  getStatusClass(house) {\n    const status = this.getHouseStatus(house);\n    return `status-${status}`;\n  }\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\n  openHouseDialog(house) {\n    if (house.CHouseId) {\n      // 使用現有的openModel方法\n      this.openModel(this.dialog, house);\n    }\n  }\n  // 新增：建立扁平化房屋資料\n  buildFlattenedHouses(data) {\n    this.flattenedHouses = [];\n    data.forEach(household => {\n      const houseType = household.CHouseHold || '';\n      household.CHouses?.forEach(house => {\n        this.flattenedHouses.push({\n          CHouseHold: houseType,\n          CBuildingName: house.CBuildingName || '未分類',\n          CHouseId: house.CHouseId || 0,\n          CFloor: house.CFloor || 0,\n          CChangeStartDate: house.CChangeStartDate || '',\n          CChangeEndDate: house.CChangeEndDate || '',\n          selected: false\n        });\n      });\n    });\n    // 初始化篩選和分頁\n    this.onSearch();\n  }\n  // 新增：視圖模式切換\n  setViewMode(mode) {\n    this.viewMode = mode;\n  }\n  // 新增：搜尋和篩選\n  onSearch() {\n    this.filteredHouses = this.flattenedHouses.filter(house => {\n      // 關鍵字搜尋\n      if (this.filterOptions.searchKeyword) {\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\n        if (!house.CHouseHold.toLowerCase().includes(keyword) && !house.CBuildingName.toLowerCase().includes(keyword)) {\n          return false;\n        }\n      }\n      // 棟別篩選\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\n        return false;\n      }\n      // 狀態篩選\n      if (this.filterOptions.statusFilter) {\n        if (!this.matchesStatusFilter(house)) {\n          return false;\n        }\n      }\n      // 樓層篩選\n      if (this.filterOptions.floorFilter) {\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\n        if (house.CFloor !== floorNumber) {\n          return false;\n        }\n      }\n      return true;\n    });\n    // 重新計算分頁\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：更新分頁\n  updatePagination() {\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\n  }\n  // 新增：頁面大小變更\n  onPageSizeChange() {\n    this.currentPage = 1;\n    this.updatePagination();\n  }\n  // 新增：跳轉頁面\n  goToPage(page) {\n    if (page >= 1 && page <= this.totalPages) {\n      this.currentPage = page;\n      this.updatePagination();\n    }\n  }\n  // 新增：取得可見頁碼\n  getVisiblePages() {\n    const pages = [];\n    const maxVisible = 5;\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\n    if (end - start + 1 < maxVisible) {\n      start = Math.max(1, end - maxVisible + 1);\n    }\n    for (let i = start; i <= end; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // 新增：全選/取消全選\n  onSelectAllChange() {\n    this.paginatedHouses.forEach(house => {\n      if (house.CHouseId) {\n        house.selected = this.selectAll;\n      }\n    });\n    this.updateSelectedHouses();\n  }\n  // 新增：單一選擇變更\n  onHouseSelectionChange() {\n    this.updateSelectedHouses();\n    this.selectAll = this.paginatedHouses.every(house => !house.CHouseId || house.selected);\n  }\n  // 新增：更新已選擇房屋列表\n  updateSelectedHouses() {\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\n  }\n  // 新增：排序\n  sort(field) {\n    if (this.sortField === field) {\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortField = field;\n      this.sortDirection = 'asc';\n    }\n    this.filteredHouses.sort((a, b) => {\n      let aValue = a[field];\n      let bValue = b[field];\n      // 處理日期排序\n      if (field.includes('Date')) {\n        aValue = aValue ? new Date(aValue).getTime() : 0;\n        bValue = bValue ? new Date(bValue).getTime() : 0;\n      }\n      // 處理數字排序\n      if (field === 'CFloor') {\n        aValue = Number(aValue) || 0;\n        bValue = Number(bValue) || 0;\n      }\n      // 處理字串排序\n      if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) {\n        return this.sortDirection === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return this.sortDirection === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    this.updatePagination();\n  }\n  // 新增：TrackBy函數\n  trackByHouseId(_index, house) {\n    return house.CHouseId;\n  }\n  // 新增：取得狀態文字\n  getStatusText(house) {\n    const status = this.getHouseStatus(house);\n    switch (status) {\n      case 'active':\n        return '進行中';\n      case 'pending':\n        return '待開放';\n      case 'expired':\n        return '已過期';\n      case 'not-set':\n        return '未設定';\n      case 'disabled':\n        return '已停用';\n      default:\n        return '未知';\n    }\n  }\n  // 新增：匯出資料\n  exportData() {\n    // 實現匯出功能\n    const csvContent = this.generateCSV();\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  // 新增：產生CSV內容\n  generateCSV() {\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\n    const rows = this.filteredHouses.map(house => [house.CHouseHold, house.CBuildingName, `${house.CFloor}F`, house.CChangeStartDate || '未設定', house.CChangeEndDate || '未設定', this.getStatusText(house)]);\n    const csvContent = [headers, ...rows].map(row => row.map(cell => `\"${cell}\"`).join(',')).join('\\n');\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\n  }\n  static {\n    this.ɵfac = function SettingTimePeriodComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SettingTimePeriodComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.MessageService), i0.ɵɵdirectiveInject(i4.ValidationHelper), i0.ɵɵdirectiveInject(i5.HouseService), i0.ɵɵdirectiveInject(i5.BuildCaseService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.EventService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SettingTimePeriodComponent,\n      selectors: [[\"ngx-setting-time-period\"]],\n      viewQuery: function SettingTimePeriodComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.batchSettingDialog = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dialog = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([]), i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 68,\n      vars: 17,\n      consts: [[\"StartDate\", \"\"], [\"EndDate\", \"\"], [\"batchSettingDialog\", \"\"], [\"dialogUpdateHousehold\", \"\"], [\"dialog\", \"\"], [\"batchStartDate\", \"\"], [\"batchEndDate\", \"\"], [\"changeStartDate\", \"\"], [\"changeEndDate\", \"\"], [\"accent\", \"success\"], [1, \"font-bold\", \"text-[#818181]\"], [1, \"query-section\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-4\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"buildingName\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u5EFA\\u6848\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"buildingSelect\", 1, \"label\", \"col-3\"], [\"placeholder\", \"\\u8ACB\\u9078\\u64C7\\u68DF\\u5225\", 1, \"col-9\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"\"], [1, \"form-group\", \"d-flex\", \"align-items-center\"], [\"for\", \"cFloorFrom\", 1, \"label\", \"col-3\"], [1, \"ml-1\"], [\"nbPrefix\", \"\", \"icon\", \"calendar-outline\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"StartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"format\", \"yyyy-MM-dd\"], [1, \"mx-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"EndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"col-md-7\", \"max-sm:col-md-12\"], [\"for\", \"cSignStatus\", 1, \"label\", \"col-3\"], [1, \"form-check\", \"mx-2\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault1\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault1\", 1, \"form-check-label\"], [\"type\", \"radio\", \"name\", \"flexRadioDefault\", \"id\", \"flexRadioDefault2\", 1, \"form-check-input\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"flexRadioDefault2\", 1, \"form-check-label\"], [1, \"col-md-5\", \"max-sm:col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", \"mx-2\", 3, \"click\"], [1, \"fas\", \"fa-search\"], [1, \"btn\", \"btn-info\", \"btn-sm\", 3, \"click\"], [\"class\", \"search-enhanced mt-3\", 4, \"ngIf\"], [\"class\", \"table-view mt-4\", 4, \"ngIf\"], [\"class\", \"card-view mt-4\", 4, \"ngIf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [3, \"value\"], [1, \"search-enhanced\", \"mt-3\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-3\"], [\"nbPrefix\", \"\", \"icon\", \"search-outline\"], [\"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6236\\u578B\\u3001\\u68DF\\u5225...\", 3, \"ngModelChange\", \"ngModel\"], [1, \"col-md-2\"], [\"placeholder\", \"\\u72C0\\u614B\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"value\", \"active\"], [\"value\", \"pending\"], [\"value\", \"expired\"], [\"value\", \"not-set\"], [\"value\", \"disabled\"], [\"placeholder\", \"\\u6A13\\u5C64\\u7BE9\\u9078\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [\"placeholder\", \"\\u986F\\u793A\\u7B46\\u6578\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"text-muted\", \"small\"], [\"class\", \"text-primary\", 4, \"ngIf\"], [1, \"view-toggle\"], [1, \"btn\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-table\"], [1, \"btn\", \"btn-sm\", \"ml-1\", 3, \"click\"], [1, \"fas\", \"fa-th\"], [1, \"text-primary\"], [1, \"table-view\", \"mt-4\"], [1, \"table-toolbar\", \"mb-3\"], [1, \"batch-actions\"], [3, \"ngModelChange\", \"ngModel\"], [1, \"btn\", \"btn-sm\", \"btn-primary\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-sm\", \"btn-success\", \"ml-2\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\"], [1, \"pagination-info\"], [1, \"table-container\"], [1, \"table\", \"table-hover\"], [1, \"table-header\"], [\"width\", \"50\"], [\"width\", \"100\", 1, \"sortable\", 3, \"click\"], [1, \"fas\", \"fa-sort\"], [\"width\", \"80\", 1, \"sortable\", 3, \"click\"], [\"width\", \"120\", 1, \"sortable\", 3, \"click\"], [\"width\", \"100\"], [\"width\", \"80\"], [3, \"table-row-selected\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"pagination-container mt-3\", 4, \"ngIf\"], [3, \"ngModelChange\", \"ngModel\", \"disabled\"], [\"class\", \"date-display\", 4, \"ngIf\"], [\"class\", \"text-muted\", 4, \"ngIf\"], [1, \"status-badge\"], [1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\", \"disabled\"], [1, \"date-display\"], [1, \"text-muted\"], [1, \"pagination-container\", \"mt-3\"], [1, \"pagination\", \"justify-content-center\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\", \"disabled\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"page-link\", 3, \"click\"], [1, \"card-view\", \"mt-4\"], [1, \"row\"], [\"class\", \"col-lg-3 col-md-4 col-sm-6 mb-3\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"col-lg-3\", \"col-md-4\", \"col-sm-6\", \"mb-3\"], [1, \"house-card\"], [1, \"p-3\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-2\"], [1, \"house-info\"], [1, \"house-number\", \"mb-1\"], [1, \"date-info\", \"mb-2\"], [1, \"date-row\"], [\"class\", \"date-value\", 4, \"ngIf\"], [1, \"status-badge\", \"small\"], [1, \"date-value\"], [1, \"text-center\", \"mt-4\"], [1, \"fas\", \"fa-info-circle\", \"fa-2x\", \"mb-3\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"sr-only\"], [1, \"mt-2\"], [2, \"width\", \"600px\", \"max-height\", \"90vh\"], [1, \"form-group\"], [1, \"text-red-600\"], [1, \"d-flex\", \"align-items-center\"], [1, \"flex-fill\"], [\"nbInput\", \"\", \"type\", \"text\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"mx-2\"], [1, \"selection-options\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\"], [1, \"btn\", \"btn-secondary\", \"me-2\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [\"class\", \"floor-selection\", 4, \"ngFor\", \"ngForOf\"], [1, \"floor-selection\"], [\"class\", \"house-selection ml-4\", 4, \"ngIf\"], [1, \"house-selection\", \"ml-4\"], [3, \"ngModel\", \"disabled\", \"ngModelChange\", 4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"500px\", \"max-height\", \"95vh\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"text-green-600\"], [1, \"px-4\"], [1, \"ml-3\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeStartDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [\"for\", \"CChangeStartDate\", 1, \"label\", \"col-1\"], [\"nbInput\", \"\", \"type\", \"text\", \"id\", \"CChangeEndDate\", \"placeholder\", \"\\u5E74/\\u6708/\\u65E5\", 1, \"w-full\", \"col-4\", 3, \"ngModelChange\", \"nbDatepicker\", \"ngModel\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", \"mx-2\", 3, \"click\"]],\n      template: function SettingTimePeriodComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 9)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"h1\", 10);\n          i0.ɵɵtext(5, \"\\u60A8\\u53EF\\u8207\\u6B64\\u7BA1\\u7406\\u5404\\u6236\\u5225\\u5167\\u4E4B\\u76F8\\u95DC\\u8CC7\\u8A0A\\uFF0C\\u5305\\u542B\\u57FA\\u672C\\u8CC7\\u6599\\u3001\\u7E73\\u6B3E\\u72C0\\u6CC1\\u3001\\u4E0A\\u50B3\\u5BA2\\u8B8A\\u5716\\u9762\\u3001\\u6AA2\\u8996\\u5BA2\\u8B8A\\u7D50\\u679C\\u7B49\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 11)(7, \"div\", 12)(8, \"div\", 13)(9, \"div\", 14)(10, \"label\", 15);\n          i0.ɵɵtext(11, \"\\u5EFA\\u6848\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"nb-select\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_12_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CBuildCaseSelected, $event) || (ctx.searchQuery.CBuildCaseSelected = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtemplate(13, SettingTimePeriodComponent_nb_option_13_Template, 2, 2, \"nb-option\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 14)(16, \"label\", 18);\n          i0.ɵɵtext(17, \"\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"nb-select\", 19);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_nb_select_ngModelChange_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedBuilding, $event) || (ctx.selectedBuilding = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function SettingTimePeriodComponent_Template_nb_select_selectedChange_18_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBuildingChange());\n          });\n          i0.ɵɵelementStart(19, \"nb-option\", 20);\n          i0.ɵɵtext(20, \"\\u5168\\u90E8\\u68DF\\u5225\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(21, SettingTimePeriodComponent_nb_option_21_Template, 2, 2, \"nb-option\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 13)(23, \"div\", 21)(24, \"label\", 22);\n          i0.ɵɵtext(25, \"\\u958B\\u653E\\u65E5\\u671F \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"nb-form-field\", 23);\n          i0.ɵɵelement(27, \"nb-icon\", 24);\n          i0.ɵɵelementStart(28, \"input\", 25);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_28_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeStartDate, $event) || (ctx.searchQuery.CChangeStartDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(29, \"nb-datepicker\", 26, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"span\", 27);\n          i0.ɵɵtext(32, \"~\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-form-field\");\n          i0.ɵɵelement(34, \"nb-icon\", 24);\n          i0.ɵɵelementStart(35, \"input\", 28);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_35_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchQuery.CChangeEndDate, $event) || (ctx.searchQuery.CChangeEndDate = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(36, \"nb-datepicker\", 26, 1);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"div\", 29)(39, \"div\", 14)(40, \"label\", 30);\n          i0.ɵɵtext(41, \" \\u7C3D\\u56DE\\u72C0\\u614B \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 31)(43, \"input\", 32);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_43_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"label\", 33);\n          i0.ɵɵtext(45, \" \\u4F9D\\u958B\\u653E\\u6642\\u6BB5\\u986F\\u793A \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 31)(47, \"input\", 34);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SettingTimePeriodComponent_Template_input_ngModelChange_47_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.isStatus, $event) || (ctx.isStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"label\", 35);\n          i0.ɵɵtext(49, \" \\u4F9D\\u958B\\u653E\\u72C0\\u614B\\u986F\\u793A \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(50, \"div\", 36)(51, \"div\", 37)(52, \"button\", 38);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_52_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.getHouseChangeDate());\n          });\n          i0.ɵɵtext(53, \" \\u67E5\\u8A62 \");\n          i0.ɵɵelement(54, \"i\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 40);\n          i0.ɵɵlistener(\"click\", function SettingTimePeriodComponent_Template_button_click_55_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openBatchSetting());\n          });\n          i0.ɵɵtext(56, \" \\u5168\\u90E8\\u6279\\u6B21\\u8A2D\\u5B9A \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(57, SettingTimePeriodComponent_div_57_Template, 47, 19, \"div\", 41)(58, SettingTimePeriodComponent_div_58_Template, 42, 31, \"div\", 42)(59, SettingTimePeriodComponent_div_59_Template, 4, 3, \"div\", 43)(60, SettingTimePeriodComponent_div_60_Template, 7, 0, \"div\", 44)(61, SettingTimePeriodComponent_div_61_Template, 9, 0, \"div\", 44);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(62, SettingTimePeriodComponent_ng_template_62_Template, 34, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(64, SettingTimePeriodComponent_ng_template_64_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(66, SettingTimePeriodComponent_ng_template_66_Template, 29, 6, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const StartDate_r31 = i0.ɵɵreference(30);\n          const EndDate_r32 = i0.ɵɵreference(37);\n          i0.ɵɵadvance(12);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CBuildCaseSelected);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.userBuildCaseOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedBuilding);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.buildingOptions);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", StartDate_r31);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeStartDate);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"nbDatepicker\", EndDate_r32);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchQuery.CChangeEndDate);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"value\", true);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"value\", false);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.isStatus);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"table\" && ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.viewMode === \"card\" && ctx.flattenedHouses.length > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.flattenedHouses.length === 0 && ctx.houseChangeDates.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i8.NgForOf, i8.NgIf, i8.DatePipe, SharedModule, i9.DefaultValueAccessor, i9.RadioControlValueAccessor, i9.NgControlStatus, i9.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbCheckboxComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbPrefixDirective, i2.NbIconComponent, i2.NbDatepickerDirective, i2.NbDatepickerComponent, i10.BreadcrumbComponent, RadioButtonModule, NbDatepickerModule, NbDateFnsDateModule],\n      styles: [\".query-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n  margin-bottom: 1rem;\\n}\\n\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n  border: 1px solid #dee2e6;\\n  border-radius: 0.375rem;\\n  overflow: hidden;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n  color: #495057;\\n  padding: 0.75rem;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem;\\n  opacity: 0.5;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   th.sortable[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr.table-row-selected[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n}\\n.table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-active[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n.table-view[_ngcontent-%COMP%]   .status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n.table-view[_ngcontent-%COMP%]   .date-display[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n  font-size: 0.875rem;\\n}\\n\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n  cursor: pointer;\\n  height: 100%;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card.selected[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card.disabled[_ngcontent-%COMP%] {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card.disabled[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n  box-shadow: none;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .house-number[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 0.25rem;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .date-info[_ngcontent-%COMP%]   .date-row[_ngcontent-%COMP%]   .date-value[_ngcontent-%COMP%] {\\n  font-family: monospace;\\n  font-size: 0.875rem;\\n  color: #495057;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-active[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  color: #856404;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-expired[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-not-set[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n.card-view[_ngcontent-%COMP%]   .house-card[_ngcontent-%COMP%]   .status-badge.status-disabled[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #6c757d;\\n}\\n\\n.search-enhanced[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  padding: 1rem;\\n  border-radius: 0.375rem;\\n  border: 1px solid #dee2e6;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-select[_ngcontent-%COMP%], \\n.search-enhanced[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.25rem;\\n}\\n.search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0.25rem;\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #495057;\\n  border-color: #dee2e6;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%]:hover {\\n  background-color: #e9ecef;\\n  border-color: #adb5bd;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.active[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n.pagination-container[_ngcontent-%COMP%]   .pagination[_ngcontent-%COMP%]   .page-item.disabled[_ngcontent-%COMP%]   .page-link[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  background-color: #fff;\\n  border-color: #dee2e6;\\n}\\n\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  padding-left: 1rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));\\n  gap: 0.25rem;\\n  margin-top: 0.5rem;\\n  padding: 0.5rem;\\n  background-color: #f8f9fa;\\n  border-radius: 0.25rem;\\n}\\n.selection-options[_ngcontent-%COMP%]   .floor-selection[_ngcontent-%COMP%]   .house-selection[_ngcontent-%COMP%]   nb-checkbox[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n\\n@media (max-width: 992px) {\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%] {\\n    overflow-x: auto;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .batch-actions[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]   .pagination-info[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .card-view[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%] {\\n    flex: 0 0 50%;\\n    max-width: 50%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .search-enhanced[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    margin-bottom: 0.5rem;\\n  }\\n  .search-enhanced[_ngcontent-%COMP%]   .view-toggle[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    margin-top: 0.5rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem 0.25rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(3), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(3) {\\n    display: none;\\n  }\\n  .card-view[_ngcontent-%COMP%]   .col-lg-3[_ngcontent-%COMP%], \\n   .card-view[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    flex: 0 0 100%;\\n    max-width: 100%;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .query-section[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:nth-child(4), \\n   .table-view[_ngcontent-%COMP%]   .table-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:nth-child(4) {\\n    display: none;\\n  }\\n}\\nnb-card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\nnb-card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  padding: 0.25rem 0.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n\\n.status-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  margin-right: 0.5rem;\\n}\\n.status-indicator.active[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n}\\n.status-indicator.pending[_ngcontent-%COMP%] {\\n  background-color: #ffc107;\\n}\\n.status-indicator.expired[_ngcontent-%COMP%] {\\n  background-color: #dc3545;\\n}\\n.status-indicator.not-set[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n}\\n.status-indicator.disabled[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "tap", "NbDatepickerModule", "NbDateFnsDateModule", "RadioButtonModule", "moment", "SharedModule", "BaseComponent", "EEvent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "case_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "CBuildCaseName", "building_r3", "floor_r6", "ctx_r4", "selectedHouses", "length", "ɵɵelement", "ɵɵtwoWayListener", "SettingTimePeriodComponent_div_57_Template_input_ngModelChange_5_listener", "$event", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "filterOptions", "searchKeyword", "ɵɵresetView", "ɵɵlistener", "onSearch", "SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_7_listener", "statusFilter", "SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_7_listener", "SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_21_listener", "floorFilter", "SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_21_listener", "ɵɵtemplate", "SettingTimePeriodComponent_div_57_nb_option_24_Template", "SettingTimePeriodComponent_div_57_Template_nb_select_ngModelChange_26_listener", "pageSize", "SettingTimePeriodComponent_div_57_Template_nb_select_selectedChange_26_listener", "onPageSizeChange", "SettingTimePeriodComponent_div_57_span_39_Template", "SettingTimePeriodComponent_div_57_Template_button_click_41_listener", "setViewMode", "SettingTimePeriodComponent_div_57_Template_button_click_44_listener", "ɵɵtwoWayProperty", "availableFloors", "filteredHouses", "ɵɵclassProp", "viewMode", "ɵɵpipeBind2", "house_r9", "CChangeStartDate", "CChangeEndDate", "SettingTimePeriodComponent_div_58_tr_40_Template_nb_checkbox_ngModelChange_2_listener", "_r8", "$implicit", "selected", "onHouseSelectionChange", "SettingTimePeriodComponent_div_58_tr_40_span_10_Template", "SettingTimePeriodComponent_div_58_tr_40_span_11_Template", "SettingTimePeriodComponent_div_58_tr_40_span_13_Template", "SettingTimePeriodComponent_div_58_tr_40_span_14_Template", "SettingTimePeriodComponent_div_58_tr_40_Template_button_click_19_listener", "dialog_r10", "ɵɵreference", "openModel", "CHouseId", "ɵɵtextInterpolate", "CHouseHold", "CBuildingName", "CFloor", "ɵɵclassMap", "getStatusClass", "getStatusText", "SettingTimePeriodComponent_div_58_div_41_li_9_Template_button_click_1_listener", "page_r13", "_r12", "goToPage", "currentPage", "SettingTimePeriodComponent_div_58_div_41_Template_button_click_4_listener", "_r11", "SettingTimePeriodComponent_div_58_div_41_Template_button_click_7_listener", "SettingTimePeriodComponent_div_58_div_41_li_9_Template", "SettingTimePeriodComponent_div_58_div_41_Template_button_click_11_listener", "SettingTimePeriodComponent_div_58_div_41_Template_button_click_14_listener", "totalPages", "getVisiblePages", "SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_4_listener", "_r7", "selectAll", "onSelectAllChange", "SettingTimePeriodComponent_div_58_Template_button_click_6_listener", "openBatchSetting", "SettingTimePeriodComponent_div_58_Template_button_click_9_listener", "exportData", "SettingTimePeriodComponent_div_58_Template_nb_checkbox_ngModelChange_19_listener", "SettingTimePeriodComponent_div_58_Template_th_click_20_listener", "sort", "SettingTimePeriodComponent_div_58_Template_th_click_23_listener", "SettingTimePeriodComponent_div_58_Template_th_click_26_listener", "SettingTimePeriodComponent_div_58_Template_th_click_29_listener", "SettingTimePeriodComponent_div_58_Template_th_click_32_listener", "SettingTimePeriodComponent_div_58_tr_40_Template", "SettingTimePeriodComponent_div_58_div_41_Template", "ɵɵtextInterpolate3", "Math", "min", "sortField", "sortDirection", "paginatedHouses", "trackByHouseId", "house_r15", "SettingTimePeriodComponent_div_59_div_2_Template_nb_checkbox_ngModelChange_9_listener", "_r14", "SettingTimePeriodComponent_div_59_div_2_span_14_Template", "SettingTimePeriodComponent_div_59_div_2_span_15_Template", "SettingTimePeriodComponent_div_59_div_2_span_19_Template", "SettingTimePeriodComponent_div_59_div_2_span_20_Template", "SettingTimePeriodComponent_div_59_div_2_Template_button_click_24_listener", "ɵɵtextInterpolate2", "SettingTimePeriodComponent_div_59_div_3_li_9_Template_button_click_1_listener", "page_r18", "_r17", "SettingTimePeriodComponent_div_59_div_3_Template_button_click_4_listener", "_r16", "SettingTimePeriodComponent_div_59_div_3_Template_button_click_7_listener", "SettingTimePeriodComponent_div_59_div_3_li_9_Template", "SettingTimePeriodComponent_div_59_div_3_Template_button_click_11_listener", "SettingTimePeriodComponent_div_59_div_3_Template_button_click_14_listener", "SettingTimePeriodComponent_div_59_div_2_Template", "SettingTimePeriodComponent_div_59_div_3_Template", "SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_nb_checkbox_1_Template_nb_checkbox_ngModelChange_0_listener", "house_r23", "_r22", "SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_nb_checkbox_1_Template", "floor_r21", "houses", "SettingTimePeriodComponent_ng_template_62_div_28_div_1_Template_nb_checkbox_ngModelChange_1_listener", "_r20", "onFloorSelectionChange", "SettingTimePeriodComponent_ng_template_62_div_28_div_1_div_3_Template", "floorNumber", "SettingTimePeriodComponent_ng_template_62_div_28_div_1_Template", "selectedBuildingForBatch", "floors", "SettingTimePeriodComponent_ng_template_62_Template_input_ngModelChange_12_listener", "_r19", "batchSettings", "startDate", "SettingTimePeriodComponent_ng_template_62_Template_input_ngModelChange_19_listener", "endDate", "SettingTimePeriodComponent_ng_template_62_Template_nb_checkbox_ngModelChange_26_listener", "applyToAll", "SettingTimePeriodComponent_ng_template_62_div_28_Template", "SettingTimePeriodComponent_ng_template_62_Template_button_click_30_listener", "ref_r24", "dialogRef", "onClose", "SettingTimePeriodComponent_ng_template_62_Template_button_click_32_listener", "onBatchSubmit", "name", "batchStartDate_r25", "batchEndDate_r26", "SettingTimePeriodComponent_ng_template_66_Template_input_ngModelChange_14_listener", "_r27", "selectedHouseChangeDate", "SettingTimePeriodComponent_ng_template_66_Template_input_ngModelChange_21_listener", "SettingTimePeriodComponent_ng_template_66_Template_button_click_25_listener", "ref_r28", "SettingTimePeriodComponent_ng_template_66_Template_button_click_27_listener", "onSubmit", "changeStartDate_r29", "changeEndDate_r30", "SettingTimePeriodComponent", "constructor", "_allow", "dialogService", "message", "valid", "_houseService", "_buildCaseService", "router", "_eventService", "tempBuildCaseId", "buildCaseOptions", "label", "value", "isStatus", "buildingGroups", "buildingOptions", "selectedBuilding", "buildingFilter", "selectedBuildings", "selectedFloors", "flattenedHouses", "loading", "undefined", "receive", "pipe", "res", "action", "payload", "subscribe", "ngOnInit", "searchQuery", "CBuildCaseSelected", "CBuildingNameSelected", "getUserBuildCase", "ref", "item", "Date", "open", "formatDate", "CChangeDate", "format", "validation", "errorMessages", "showErrorMSGs", "param", "apiHouseSaveHouseChangeDatePost$Json", "body", "StatusCode", "showSucessMSG", "getHouseChangeDate", "close", "apiBuildCaseGetUserBuildCasePost$Json", "entries", "Entries", "userBuildCaseOptions", "map", "entry", "cID", "index", "findIndex", "x", "selectedCID", "convertHouseholdArrayOptimized", "arr", "floorDict", "for<PERSON>ach", "household", "CHouses", "house", "floor", "push", "a", "b", "result", "households", "find", "h", "getFloorsAndHouseholds", "floorsSet", "Set", "householdsSet", "add", "Array", "from", "validationDate", "apiHouseGetHouseChangeDatePost$Json", "CBuildCaseId", "houseChangeDates", "convertedHouseArray", "buildBuildingGroups", "buildFlattenedHouses", "data", "buildingMap", "Map", "houseType", "buildingName", "has", "set", "floorMap", "get", "localeCompare", "bg", "updateAvailableFloors", "building", "onBuildingChange", "getFilteredBuildings", "filter", "keyword", "toLowerCase", "hasMatchingHouse", "some", "includes", "hasMatchingStatus", "matchesStatusFilter", "parseInt", "hasMatchingFloor", "filteredBuilding", "hasValidHouses", "filteredFloor", "status", "getHouseStatus", "now", "clear", "required", "checkStartBeforeEnd", "onNavigateWithId", "navigate", "batchSettingDialog", "housesToUpdate", "openHouseDialog", "dialog", "mode", "updatePagination", "ceil", "startIndex", "endIndex", "slice", "page", "pages", "maxVisible", "start", "max", "end", "i", "updateSelectedHouses", "every", "field", "aValue", "bValue", "getTime", "Number", "_index", "csv<PERSON><PERSON>nt", "generateCSV", "blob", "Blob", "type", "link", "document", "createElement", "url", "URL", "createObjectURL", "setAttribute", "toISOString", "split", "style", "visibility", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "rows", "row", "cell", "join", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "MessageService", "i4", "ValidationHelper", "i5", "HouseService", "BuildCaseService", "i6", "Router", "i7", "EventService", "selectors", "viewQuery", "SettingTimePeriodComponent_Query", "rf", "ctx", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SettingTimePeriodComponent_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_12_listener", "_r1", "SettingTimePeriodComponent_Template_nb_select_selectedChange_12_listener", "SettingTimePeriodComponent_nb_option_13_Template", "SettingTimePeriodComponent_Template_nb_select_ngModelChange_18_listener", "SettingTimePeriodComponent_Template_nb_select_selected<PERSON><PERSON>e_18_listener", "SettingTimePeriodComponent_nb_option_21_Template", "SettingTimePeriodComponent_Template_input_ngModelChange_28_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_35_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_43_listener", "SettingTimePeriodComponent_Template_input_ngModelChange_47_listener", "SettingTimePeriodComponent_Template_button_click_52_listener", "SettingTimePeriodComponent_Template_button_click_55_listener", "SettingTimePeriodComponent_div_57_Template", "SettingTimePeriodComponent_div_58_Template", "SettingTimePeriodComponent_div_59_Template", "SettingTimePeriodComponent_div_60_Template", "SettingTimePeriodComponent_div_61_Template", "SettingTimePeriodComponent_ng_template_62_Template", "ɵɵtemplateRefExtractor", "SettingTimePeriodComponent_ng_template_64_Template", "SettingTimePeriodComponent_ng_template_66_Template", "StartDate_r31", "EndDate_r32", "i8", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DatePipe", "i9", "DefaultValueAccessor", "RadioControlValueAccessor", "NgControlStatus", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbCheckboxComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbPrefixDirective", "NbIconComponent", "NbDatepickerDirective", "NbDatepickerComponent", "i10", "BreadcrumbComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\selection-management\\setting-time-period\\setting-time-period.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { tap } from 'rxjs';\r\nimport { NbDialogService, NbDatepickerModule } from '@nebular/theme';\r\nimport { NbDateFnsDateModule } from '@nebular/date-fns';\r\nimport { RadioButtonModule } from 'primeng/radiobutton';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { BuildCaseService, HouseService } from 'src/services/api/services';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { GetHouseChangeDateRes } from 'src/services/api/models';\r\nimport { SettingTimeStatusPipe } from 'src/app/@theme/pipes/mapping.pipe';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { BaseComponent } from '../../components/base/baseComponent';\r\nimport { EEvent, EventService, IEvent } from 'src/app/shared/services/event.service';\r\n\r\nexport interface ConvertedHouse {\r\n  CHouseHold: string; // 戶型\r\n  CBuildingName: string; // 棟別\r\n  CHouseId: number;\r\n  CFloor: number;\r\n  CChangeStartDate: string;\r\n  CChangeEndDate: string;\r\n  selected?: boolean; // 用於批次選擇\r\n}\r\n\r\nexport interface HouseChangeDateReq {\r\n  CBuildCaseSelected?: any | null;\r\n  CBuildingNameSelected?: any | null;\r\n  CChangeStartDate?: any | null;\r\n  CChangeEndDate?: any | null;\r\n}\r\n\r\n// 新增的介面定義\r\nexport interface BuildingGroup {\r\n  name: string;\r\n  floors: FloorGroup[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface FloorGroup {\r\n  floorNumber: number;\r\n  houses: ConvertedHouse[];\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface BatchSettings {\r\n  startDate: any;\r\n  endDate: any;\r\n  applyToAll: boolean;\r\n  selectedBuildings: string[];\r\n  selectedFloors: number[];\r\n  selectedHouses: number[];\r\n}\r\n\r\nexport interface FilterOptions {\r\n  searchKeyword: string;\r\n  statusFilter: string;\r\n  floorFilter: string;\r\n  buildingFilter: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-setting-time-period',\r\n  templateUrl: './setting-time-period.component.html',\r\n  styleUrls: ['./setting-time-period.component.scss'],\r\n  standalone: true,\r\n  providers: [],\r\n  imports: [\r\n    CommonModule, SharedModule, RadioButtonModule,\r\n    NbDatepickerModule, NbDateFnsDateModule,\r\n    SettingTimeStatusPipe,\r\n  ],\r\n})\r\n\r\nexport class SettingTimePeriodComponent extends BaseComponent implements OnInit {\r\n  tempBuildCaseId: number = -1;\r\n\r\n  @ViewChild('batchSettingDialog') batchSettingDialog!: TemplateRef<any>;\r\n  @ViewChild('dialog') dialog!: TemplateRef<any>;\r\n  constructor(\r\n    private _allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper,\r\n    private _houseService: HouseService,\r\n    private _buildCaseService: BuildCaseService,\r\n    private router: Router,\r\n    private _eventService: EventService\r\n\r\n  ) {\r\n    super(_allow)\r\n\r\n    this.selectedHouseChangeDate = {\r\n      CChangeStartDate: '',\r\n      CChangeEndDate: '',\r\n      CFloor: undefined,\r\n      CHouseHold: '',\r\n      CHouseId: undefined,\r\n    }\r\n\r\n    this._eventService.receive().pipe(\r\n      tap((res: IEvent) => {\r\n        if (res.action === EEvent.GET_BUILDCASE) {\r\n          this.tempBuildCaseId = res.payload\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  searchQuery: HouseChangeDateReq\r\n  detailSelected: HouseChangeDateReq\r\n\r\n  selectedHouseChangeDate: {\r\n    CHouseHold: string | undefined;\r\n    CHouseId: number | undefined;\r\n    CFloor: number | undefined;\r\n    CChangeStartDate: any;\r\n    CChangeEndDate: any;\r\n  }\r\n\r\n  buildCaseOptions: any[] = [{ label: '全部', value: '' }]\r\n\r\n  userBuildCaseOptions: any\r\n\r\n  isStatus: boolean = true;\r\n  userBuildCaseSelected: any\r\n  houseChangeDates: GetHouseChangeDateRes[]\r\n  convertedHouseArray: ConvertedHouse[][]\r\n  floors: any\r\n  households: any\r\n\r\n  // 新增的屬性\r\n  buildingGroups: BuildingGroup[] = [];\r\n  buildingOptions: string[] = [];\r\n  selectedBuilding: string = '';\r\n  availableFloors: number[] = [];\r\n\r\n  // 篩選和搜尋\r\n  filterOptions: FilterOptions = {\r\n    searchKeyword: '',\r\n    statusFilter: '',\r\n    floorFilter: '',\r\n    buildingFilter: ''\r\n  };\r\n\r\n  // 批次設定\r\n  batchSettings: BatchSettings = {\r\n    startDate: null,\r\n    endDate: null,\r\n    applyToAll: true,\r\n    selectedBuildings: [],\r\n    selectedFloors: [],\r\n    selectedHouses: []\r\n  };\r\n\r\n  selectedBuildingForBatch: BuildingGroup | null = null;\r\n\r\n  // 新增：表格視圖相關屬性\r\n  viewMode: 'table' | 'card' = 'table';\r\n  flattenedHouses: ConvertedHouse[] = [];\r\n  filteredHouses: ConvertedHouse[] = [];\r\n  paginatedHouses: ConvertedHouse[] = [];\r\n  selectedHouses: ConvertedHouse[] = [];\r\n  selectAll: boolean = false;\r\n  loading: boolean = false;\r\n\r\n  // 分頁相關\r\n  currentPage: number = 1;\r\n  pageSize: number = 100;\r\n  totalPages: number = 1;\r\n\r\n  // 排序相關\r\n  sortField: string = '';\r\n  sortDirection: 'asc' | 'desc' = 'asc';\r\n\r\n  // 數學函數引用\r\n  Math = Math;\r\n\r\n  override ngOnInit(): void {\r\n    this.searchQuery = {\r\n      CBuildCaseSelected: null,\r\n      CBuildingNameSelected: this.buildCaseOptions[0],\r\n      CChangeStartDate: undefined,\r\n      CChangeEndDate: undefined\r\n    }\r\n    this.getUserBuildCase()\r\n  }\r\n\r\n  openModel(ref: any, item: any) {\r\n    if (item.CHouseId) {\r\n      this.selectedHouseChangeDate = {\r\n        ...item,\r\n        CChangeStartDate: item.CChangeStartDate ? new Date(item.CChangeStartDate) : undefined,\r\n        CChangeEndDate: item.CChangeEndDate ? new Date(item.CChangeEndDate) : undefined,\r\n      }\r\n      this.dialogService.open(ref)\r\n    }\r\n  }\r\n\r\n  formatDate(CChangeDate: string): string {\r\n    if (CChangeDate) {\r\n      return moment(CChangeDate).format('YYYY-MM-DDTHH:mm:ss')\r\n    }\r\n    return ''\r\n  }\r\n\r\n  onSubmit(ref: any) {\r\n    this.validation()\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return\r\n    }\r\n    const param = {\r\n      CHouseId: this.selectedHouseChangeDate.CHouseId,\r\n      CChangeStartDate: this.formatDate(this.selectedHouseChangeDate.CChangeStartDate),\r\n      CChangeEndDate: this.formatDate(this.selectedHouseChangeDate.CChangeEndDate),\r\n    }\r\n\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: [param]\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(\"執行成功\");\r\n        this.getHouseChangeDate()\r\n        ref.close();\r\n      }\r\n    })\r\n  }\r\n\r\n  getUserBuildCase() {\r\n    this._buildCaseService.apiBuildCaseGetUserBuildCasePost$Json({ body: {} }).pipe(\r\n      tap(res => {\r\n        const entries = res.Entries ?? []; // entries not undefined and not null\r\n        if (entries.length && res.StatusCode === 0) {\r\n          this.userBuildCaseOptions = entries.map(entry => ({\r\n            CBuildCaseName: entry.CBuildCaseName,\r\n            cID: entry.cID\r\n          }));\r\n          if (this.tempBuildCaseId != -1) {\r\n            let index = this.userBuildCaseOptions.findIndex((x: any) => x.cID == this.tempBuildCaseId)\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[index] ?? null;\r\n          } else {\r\n            this.searchQuery.CBuildCaseSelected = this.userBuildCaseOptions[0] ?? null;\r\n          }\r\n          const selectedCID = this.searchQuery?.CBuildCaseSelected?.cID;\r\n          if (selectedCID) {\r\n            this.getHouseChangeDate();\r\n          }\r\n        }\r\n      }),\r\n    ).subscribe();\r\n  }\r\n\r\n  convertHouseholdArrayOptimized(arr: any[]): ConvertedHouse[][] {\r\n    const floorDict: { [key: number]: ConvertedHouse[] } = {}; // Initialize dictionary to group elements by CFloor\r\n    arr.forEach(household => {\r\n      household.CHouses.forEach((house: any) => {\r\n        const floor = house.CFloor;\r\n        if (!floorDict[floor]) { // If CFloor is not in the dictionary, initialize an empty list\r\n          floorDict[floor] = [];\r\n        }\r\n        floorDict[floor].push({ // Add element to list of corresponding CFloor\r\n          CHouseHold: household.CHouseHold,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId,\r\n          CFloor: house.CFloor,\r\n          CChangeStartDate: house.CChangeStartDate,\r\n          CChangeEndDate: house.CChangeEndDate\r\n        });\r\n      });\r\n    });\r\n\r\n    // Arrange floors in ascending order\r\n    this.floors.sort((a: any, b: any) => b - a);\r\n    const result: ConvertedHouse[][] = this.floors.map((floor: any) => {\r\n      return this.households.map((household: any) => {\r\n        const house = floorDict[floor].find((h: { CHouseHold: any; }) => h.CHouseHold === household);\r\n        return house || {\r\n          CHouseHold: household,\r\n          CBuildingName: '未分類',\r\n          CHouseId: null,\r\n          CFloor: floor,\r\n          CChangeStartDate: null,\r\n          CChangeEndDate: null\r\n        } as unknown as ConvertedHouse;\r\n      });\r\n    });\r\n    return result;\r\n  }\r\n\r\n  getFloorsAndHouseholds(arr: any[]): { floors: number[], households: string[] } {\r\n    const floorsSet: Set<number> = new Set();\r\n    const householdsSet: Set<string> = new Set();\r\n\r\n    arr.forEach(household => {\r\n      householdsSet.add(household.CHouseHold);\r\n      household.CHouses.forEach((house: any) => {\r\n        floorsSet.add(house.CFloor);\r\n      });\r\n    });\r\n    this.floors = Array.from(floorsSet)\r\n    this.households = Array.from(householdsSet)\r\n\r\n    return {\r\n      floors: Array.from(floorsSet),\r\n      households: Array.from(householdsSet)\r\n    };\r\n  }\r\n\r\n  validationDate() {\r\n    if (this.searchQuery.CChangeStartDate && this.searchQuery.CChangeEndDate) {\r\n      const startDate = new Date(this.searchQuery.CChangeStartDate);\r\n      const endDate = new Date(this.searchQuery.CChangeEndDate);\r\n      if (startDate && endDate && startDate > endDate) {\r\n        this.message.showErrorMSGs([`結束日期不能小於起始日期!`])\r\n      }\r\n    }\r\n  }\r\n\r\n  getHouseChangeDate() {\r\n    this.loading = true;\r\n    this.validationDate()\r\n    this._houseService.apiHouseGetHouseChangeDatePost$Json({\r\n      body: {\r\n        CBuildCaseId: this.searchQuery.CBuildCaseSelected.cID,\r\n        CChangeStartDate: this.searchQuery.CChangeStartDate ? this.formatDate(this.searchQuery.CChangeStartDate) : undefined,\r\n        CChangeEndDate: this.searchQuery.CChangeEndDate ? this.formatDate(this.searchQuery.CChangeEndDate) : undefined,\r\n      }\r\n    }).subscribe(res => {\r\n      this.loading = false;\r\n      if (res.Entries && res.StatusCode == 0) {\r\n        this.houseChangeDates = res.Entries ? res.Entries : []\r\n        if (res.Entries) {\r\n          this.houseChangeDates = [...res.Entries]\r\n          this.getFloorsAndHouseholds(res.Entries)\r\n          this.convertedHouseArray = this.convertHouseholdArrayOptimized(res.Entries)\r\n          // 新增：建立棟別分組資料\r\n          this.buildBuildingGroups(res.Entries)\r\n          // 新增：建立扁平化資料\r\n          this.buildFlattenedHouses(res.Entries)\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  // 修正：建立棟別分組資料 - 使用CBuildingName作為棟別\r\n  buildBuildingGroups(data: GetHouseChangeDateRes[]) {\r\n    const buildingMap = new Map<string, Map<number, ConvertedHouse[]>>();\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || ''; // 戶型\r\n\r\n      household.CHouses?.forEach(house => {\r\n        const buildingName = house.CBuildingName || '未分類'; // 棟別\r\n        const floor = house.CFloor || 0;\r\n\r\n        if (!buildingMap.has(buildingName)) {\r\n          buildingMap.set(buildingName, new Map<number, ConvertedHouse[]>());\r\n        }\r\n\r\n        const floorMap = buildingMap.get(buildingName)!;\r\n\r\n        if (!floorMap.has(floor)) {\r\n          floorMap.set(floor, []);\r\n        }\r\n\r\n        floorMap.get(floor)!.push({\r\n          CHouseHold: houseType, // 戶型\r\n          CBuildingName: buildingName, // 棟別\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: floor,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 轉換為BuildingGroup格式\r\n    this.buildingGroups = Array.from(buildingMap.entries()).map(([buildingName, floorMap]) => {\r\n      const floors: FloorGroup[] = Array.from(floorMap.entries())\r\n        .sort(([a], [b]) => b - a) // 樓層由高到低排序\r\n        .map(([floorNumber, houses]) => ({\r\n          floorNumber,\r\n          houses: houses.sort((a, b) => {\r\n            // 排序邏輯：先按戶型排序，再按樓層排序\r\n            if (a.CHouseHold !== b.CHouseHold) {\r\n              return a.CHouseHold.localeCompare(b.CHouseHold);\r\n            }\r\n            return a.CFloor - b.CFloor;\r\n          }),\r\n          selected: false\r\n        }));\r\n\r\n      return {\r\n        name: buildingName,\r\n        floors,\r\n        selected: false\r\n      };\r\n    }).sort((a, b) => a.name.localeCompare(b.name));\r\n\r\n    // 更新棟別選項和可用樓層\r\n    this.buildingOptions = this.buildingGroups.map(bg => bg.name);\r\n    this.updateAvailableFloors();\r\n  }\r\n\r\n  // 新增：更新可用樓層\r\n  updateAvailableFloors() {\r\n    const floorsSet = new Set<number>();\r\n\r\n    this.buildingGroups.forEach(building => {\r\n      if (!this.selectedBuilding || building.name === this.selectedBuilding) {\r\n        building.floors.forEach(floor => {\r\n          floorsSet.add(floor.floorNumber);\r\n        });\r\n      }\r\n    });\r\n\r\n    this.availableFloors = Array.from(floorsSet).sort((a, b) => b - a);\r\n  }\r\n\r\n  // 新增：棟別選擇變更處理\r\n  onBuildingChange() {\r\n    this.updateAvailableFloors();\r\n    this.filterOptions.buildingFilter = this.selectedBuilding;\r\n  }\r\n\r\n\r\n\r\n  // 新增：取得過濾後的棟別資料\r\n  getFilteredBuildings(): BuildingGroup[] {\r\n    return this.buildingGroups.filter(building => {\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && building.name !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 關鍵字搜尋 (搜尋戶型)\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        const hasMatchingHouse = building.floors.some(floor =>\r\n          floor.houses.some(house =>\r\n            house.CHouseHold.toLowerCase().includes(keyword) ||\r\n            house.CBuildingName.toLowerCase().includes(keyword)\r\n          )\r\n        );\r\n        if (!hasMatchingHouse) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        const hasMatchingStatus = building.floors.some(floor =>\r\n          floor.houses.some(house => this.matchesStatusFilter(house))\r\n        );\r\n        if (!hasMatchingStatus) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        const hasMatchingFloor = building.floors.some(floor =>\r\n          floor.floorNumber === floorNumber\r\n        );\r\n        if (!hasMatchingFloor) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    }).map(building => {\r\n      // 對每個棟別，也要篩選其樓層和戶別\r\n      const filteredBuilding = { ...building };\r\n      filteredBuilding.floors = building.floors.filter(floor => {\r\n        // 樓層篩選\r\n        if (this.filterOptions.floorFilter) {\r\n          const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n          if (floor.floorNumber !== floorNumber) {\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 檢查該樓層是否有符合條件的戶別\r\n        const hasValidHouses = floor.houses.some(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return hasValidHouses;\r\n      }).map(floor => {\r\n        // 篩選戶別\r\n        const filteredFloor = { ...floor };\r\n        filteredFloor.houses = floor.houses.filter(house => {\r\n          // 關鍵字篩選 (搜尋戶型或棟別)\r\n          if (this.filterOptions.searchKeyword) {\r\n            const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n            if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n              !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          // 狀態篩選\r\n          if (this.filterOptions.statusFilter) {\r\n            if (!this.matchesStatusFilter(house)) {\r\n              return false;\r\n            }\r\n          }\r\n\r\n          return true;\r\n        });\r\n\r\n        return filteredFloor;\r\n      });\r\n\r\n      return filteredBuilding;\r\n    });\r\n  }\r\n\r\n  // 新增：檢查戶別是否符合狀態篩選\r\n  private matchesStatusFilter(house: ConvertedHouse): boolean {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (this.filterOptions.statusFilter) {\r\n      case 'active':\r\n        return status === 'active';\r\n      case 'inactive':\r\n        return status === 'not-set';\r\n      case 'disabled':\r\n        return status === 'disabled';\r\n      default:\r\n        return true;\r\n    }\r\n  }\r\n\r\n  // 新增：取得戶別狀態\r\n  private getHouseStatus(house: ConvertedHouse): string {\r\n    if (!house.CHouseId) {\r\n      return 'disabled';\r\n    }\r\n\r\n    if (house.CChangeStartDate && house.CChangeEndDate) {\r\n      const now = new Date();\r\n      const startDate = new Date(house.CChangeStartDate);\r\n      const endDate = new Date(house.CChangeEndDate);\r\n\r\n      if (now < startDate) {\r\n        return 'pending';\r\n      } else if (now >= startDate && now <= endDate) {\r\n        return 'active';\r\n      } else {\r\n        return 'expired';\r\n      }\r\n    }\r\n\r\n    return 'not-set';\r\n  }\r\n\r\n  onClose(ref: any) {\r\n    ref.close();\r\n  }\r\n\r\n  validation() {\r\n    this.valid.clear();\r\n    this.valid.required('[客變開始日期]', this.selectedHouseChangeDate.CChangeStartDate)\r\n    this.valid.required('[客變結束日期]', this.selectedHouseChangeDate.CChangeEndDate)\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.selectedHouseChangeDate.CChangeStartDate, this.selectedHouseChangeDate.CChangeEndDate)\r\n  }\r\n\r\n  onNavigateWithId() {\r\n    this.router.navigate([`/pages/setting-time-period/${this.searchQuery?.CBuildCaseSelected?.cID}`])\r\n  }\r\n\r\n  // 新增：開啟批次設定對話框\r\n  openBatchSetting(building?: BuildingGroup) {\r\n    this.selectedBuildingForBatch = building || null;\r\n    this.batchSettings = {\r\n      startDate: null,\r\n      endDate: null,\r\n      applyToAll: !building,\r\n      selectedBuildings: building ? [building.name] : [],\r\n      selectedFloors: [],\r\n      selectedHouses: []\r\n    };\r\n\r\n    // 重置選擇狀態\r\n    if (building) {\r\n      building.floors.forEach(floor => {\r\n        floor.selected = false;\r\n        floor.houses.forEach(house => house.selected = false);\r\n      });\r\n    }\r\n\r\n    // 開啟對話框\r\n    this.dialogService.open(this.batchSettingDialog);\r\n  }\r\n\r\n  // 新增：樓層選擇變更處理\r\n  onFloorSelectionChange(floor: FloorGroup) {\r\n    if (floor.selected) {\r\n      floor.houses.forEach(house => {\r\n        if (house.CHouseId) {\r\n          house.selected = true;\r\n        }\r\n      });\r\n    } else {\r\n      floor.houses.forEach(house => house.selected = false);\r\n    }\r\n  }\r\n\r\n  // 新增：批次提交\r\n  onBatchSubmit(ref: any) {\r\n    // 驗證批次設定\r\n    this.valid.clear();\r\n    this.valid.required('[開始日期]', this.batchSettings.startDate);\r\n    this.valid.required('[結束日期]', this.batchSettings.endDate);\r\n    this.valid.checkStartBeforeEnd('[開放日期]', this.batchSettings.startDate, this.batchSettings.endDate);\r\n\r\n    if (this.valid.errorMessages.length > 0) {\r\n      this.message.showErrorMSGs(this.valid.errorMessages);\r\n      return;\r\n    }\r\n\r\n    // 收集要更新的房屋\r\n    const housesToUpdate: any[] = [];\r\n\r\n    if (this.batchSettings.applyToAll) {\r\n      // 全部戶別\r\n      this.buildingGroups.forEach(building => {\r\n        building.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      });\r\n    } else {\r\n      // 選擇的戶別\r\n      if (this.selectedBuildingForBatch) {\r\n        this.selectedBuildingForBatch.floors.forEach(floor => {\r\n          floor.houses.forEach(house => {\r\n            if (house.selected && house.CHouseId) {\r\n              housesToUpdate.push({\r\n                CHouseId: house.CHouseId,\r\n                CChangeStartDate: this.formatDate(this.batchSettings.startDate),\r\n                CChangeEndDate: this.formatDate(this.batchSettings.endDate)\r\n              });\r\n            }\r\n          });\r\n        });\r\n      }\r\n    }\r\n\r\n    if (housesToUpdate.length === 0) {\r\n      this.message.showErrorMSGs(['請選擇要設定的戶別']);\r\n      return;\r\n    }\r\n\r\n    // 調用API進行批次更新\r\n    this._houseService.apiHouseSaveHouseChangeDatePost$Json({\r\n      body: housesToUpdate\r\n    }).subscribe(res => {\r\n      if (res.StatusCode == 0) {\r\n        this.message.showSucessMSG(`成功設定 ${housesToUpdate.length} 個戶別的開放時段`);\r\n        this.getHouseChangeDate();\r\n        ref.close();\r\n      }\r\n    });\r\n  }\r\n\r\n  // 新增：取得狀態樣式類別\r\n  getStatusClass(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n    return `status-${status}`;\r\n  }\r\n\r\n  // 修改：開啟單一戶別設定對話框 (使用現有的openModel方法)\r\n  openHouseDialog(house: ConvertedHouse) {\r\n    if (house.CHouseId) {\r\n      // 使用現有的openModel方法\r\n      this.openModel(this.dialog, house);\r\n    }\r\n  }\r\n\r\n  // 新增：建立扁平化房屋資料\r\n  buildFlattenedHouses(data: GetHouseChangeDateRes[]) {\r\n    this.flattenedHouses = [];\r\n\r\n    data.forEach(household => {\r\n      const houseType = household.CHouseHold || '';\r\n\r\n      household.CHouses?.forEach(house => {\r\n        this.flattenedHouses.push({\r\n          CHouseHold: houseType,\r\n          CBuildingName: house.CBuildingName || '未分類',\r\n          CHouseId: house.CHouseId || 0,\r\n          CFloor: house.CFloor || 0,\r\n          CChangeStartDate: house.CChangeStartDate || '',\r\n          CChangeEndDate: house.CChangeEndDate || '',\r\n          selected: false\r\n        });\r\n      });\r\n    });\r\n\r\n    // 初始化篩選和分頁\r\n    this.onSearch();\r\n  }\r\n\r\n  // 新增：視圖模式切換\r\n  setViewMode(mode: 'table' | 'card') {\r\n    this.viewMode = mode;\r\n  }\r\n\r\n  // 新增：搜尋和篩選\r\n  onSearch() {\r\n    this.filteredHouses = this.flattenedHouses.filter(house => {\r\n      // 關鍵字搜尋\r\n      if (this.filterOptions.searchKeyword) {\r\n        const keyword = this.filterOptions.searchKeyword.toLowerCase();\r\n        if (!house.CHouseHold.toLowerCase().includes(keyword) &&\r\n          !house.CBuildingName.toLowerCase().includes(keyword)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 棟別篩選\r\n      if (this.selectedBuilding && house.CBuildingName !== this.selectedBuilding) {\r\n        return false;\r\n      }\r\n\r\n      // 狀態篩選\r\n      if (this.filterOptions.statusFilter) {\r\n        if (!this.matchesStatusFilter(house)) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      // 樓層篩選\r\n      if (this.filterOptions.floorFilter) {\r\n        const floorNumber = parseInt(this.filterOptions.floorFilter);\r\n        if (house.CFloor !== floorNumber) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    });\r\n\r\n    // 重新計算分頁\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：更新分頁\r\n  updatePagination() {\r\n    this.totalPages = Math.ceil(this.filteredHouses.length / this.pageSize);\r\n    const startIndex = (this.currentPage - 1) * this.pageSize;\r\n    const endIndex = startIndex + this.pageSize;\r\n    this.paginatedHouses = this.filteredHouses.slice(startIndex, endIndex);\r\n  }\r\n\r\n  // 新增：頁面大小變更\r\n  onPageSizeChange() {\r\n    this.currentPage = 1;\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：跳轉頁面\r\n  goToPage(page: number) {\r\n    if (page >= 1 && page <= this.totalPages) {\r\n      this.currentPage = page;\r\n      this.updatePagination();\r\n    }\r\n  }\r\n\r\n  // 新增：取得可見頁碼\r\n  getVisiblePages(): number[] {\r\n    const pages: number[] = [];\r\n    const maxVisible = 5;\r\n    let start = Math.max(1, this.currentPage - Math.floor(maxVisible / 2));\r\n    let end = Math.min(this.totalPages, start + maxVisible - 1);\r\n\r\n    if (end - start + 1 < maxVisible) {\r\n      start = Math.max(1, end - maxVisible + 1);\r\n    }\r\n\r\n    for (let i = start; i <= end; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n  // 新增：全選/取消全選\r\n  onSelectAllChange() {\r\n    this.paginatedHouses.forEach(house => {\r\n      if (house.CHouseId) {\r\n        house.selected = this.selectAll;\r\n      }\r\n    });\r\n    this.updateSelectedHouses();\r\n  }\r\n\r\n  // 新增：單一選擇變更\r\n  onHouseSelectionChange() {\r\n    this.updateSelectedHouses();\r\n    this.selectAll = this.paginatedHouses.every(house =>\r\n      !house.CHouseId || house.selected\r\n    );\r\n  }\r\n\r\n  // 新增：更新已選擇房屋列表\r\n  updateSelectedHouses() {\r\n    this.selectedHouses = this.flattenedHouses.filter(house => house.selected);\r\n  }\r\n\r\n  // 新增：排序\r\n  sort(field: string) {\r\n    if (this.sortField === field) {\r\n      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';\r\n    } else {\r\n      this.sortField = field;\r\n      this.sortDirection = 'asc';\r\n    }\r\n\r\n    this.filteredHouses.sort((a, b) => {\r\n      let aValue = (a as any)[field];\r\n      let bValue = (b as any)[field];\r\n\r\n      // 處理日期排序\r\n      if (field.includes('Date')) {\r\n        aValue = aValue ? new Date(aValue).getTime() : 0;\r\n        bValue = bValue ? new Date(bValue).getTime() : 0;\r\n      }\r\n\r\n      // 處理數字排序\r\n      if (field === 'CFloor') {\r\n        aValue = Number(aValue) || 0;\r\n        bValue = Number(bValue) || 0;\r\n      }\r\n\r\n      // 處理字串排序\r\n      if (typeof aValue === 'string') {\r\n        aValue = aValue.toLowerCase();\r\n        bValue = bValue.toLowerCase();\r\n      }\r\n\r\n      if (aValue < bValue) {\r\n        return this.sortDirection === 'asc' ? -1 : 1;\r\n      }\r\n      if (aValue > bValue) {\r\n        return this.sortDirection === 'asc' ? 1 : -1;\r\n      }\r\n      return 0;\r\n    });\r\n\r\n    this.updatePagination();\r\n  }\r\n\r\n  // 新增：TrackBy函數\r\n  trackByHouseId(_index: number, house: ConvertedHouse): number {\r\n    return house.CHouseId;\r\n  }\r\n\r\n  // 新增：取得狀態文字\r\n  getStatusText(house: ConvertedHouse): string {\r\n    const status = this.getHouseStatus(house);\r\n\r\n    switch (status) {\r\n      case 'active':\r\n        return '進行中';\r\n      case 'pending':\r\n        return '待開放';\r\n      case 'expired':\r\n        return '已過期';\r\n      case 'not-set':\r\n        return '未設定';\r\n      case 'disabled':\r\n        return '已停用';\r\n      default:\r\n        return '未知';\r\n    }\r\n  }\r\n\r\n  // 新增：匯出資料\r\n  exportData() {\r\n    // 實現匯出功能\r\n    const csvContent = this.generateCSV();\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute('download', `選樣開放時段_${new Date().toISOString().split('T')[0]}.csv`);\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  // 新增：產生CSV內容\r\n  private generateCSV(): string {\r\n    const headers = ['戶型', '棟別', '樓層', '開始日期', '結束日期', '狀態'];\r\n    const rows = this.filteredHouses.map(house => [\r\n      house.CHouseHold,\r\n      house.CBuildingName,\r\n      `${house.CFloor}F`,\r\n      house.CChangeStartDate || '未設定',\r\n      house.CChangeEndDate || '未設定',\r\n      this.getStatusText(house)\r\n    ]);\r\n\r\n    const csvContent = [headers, ...rows]\r\n      .map(row => row.map(cell => `\"${cell}\"`).join(','))\r\n      .join('\\n');\r\n\r\n    return '\\uFEFF' + csvContent; // 加入BOM以支援中文\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <h1 class=\"font-bold text-[#818181]\">您可與此管理各戶別內之相關資訊，包含基本資料、繳款狀況、上傳客變圖面、檢視客變結果等等。 </h1>\r\n\r\n    <!-- 查詢條件區域 -->\r\n    <div class=\"query-section\">\r\n      <div class=\"d-flex flex-wrap\">\r\n        <div class=\"col-md-4\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingName\" class=\"label col-3\">建案</label>\r\n            <nb-select placeholder=\"建案\" [(ngModel)]=\"searchQuery.CBuildCaseSelected\" class=\"col-9\"\r\n              (selectedChange)=\"getHouseChangeDate()\">\r\n              <nb-option *ngFor=\"let case of userBuildCaseOptions\" [value]=\"case\">\r\n                {{ case.CBuildCaseName }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-4\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"buildingSelect\" class=\"label col-3\">棟別</label>\r\n            <nb-select placeholder=\"請選擇棟別\" [(ngModel)]=\"selectedBuilding\" class=\"col-9\"\r\n              (selectedChange)=\"onBuildingChange()\">\r\n              <nb-option value=\"\">全部棟別</nb-option>\r\n              <nb-option *ngFor=\"let building of buildingOptions\" [value]=\"building\">\r\n                {{ building }}\r\n              </nb-option>\r\n            </nb-select>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-4\">\r\n          <div class=\"form-group d-flex align-items-center\">\r\n            <label for=\"cFloorFrom\" class=\"label col-3\">開放日期\r\n            </label>\r\n            <nb-form-field class=\"ml-1\">\r\n              <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n              <input nbInput type=\"text\" id=\"StartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"StartDate\" class=\"w-full\"\r\n                [(ngModel)]=\"searchQuery.CChangeStartDate\">\r\n              <nb-datepicker #StartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n            </nb-form-field>\r\n            <span class=\"mx-1\">~</span>\r\n            <nb-form-field>\r\n              <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n              <input nbInput type=\"text\" id=\"EndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"EndDate\" class=\"w-full\"\r\n                [(ngModel)]=\"searchQuery.CChangeEndDate\">\r\n              <nb-datepicker #EndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n            </nb-form-field>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-7 max-sm:col-md-12\">\r\n          <div class=\"form-group d-flex align-items-center w-full\">\r\n            <label for=\"cSignStatus\" class=\"label col-3\">\r\n              簽回狀態\r\n            </label>\r\n            <div class=\"form-check mx-2\">\r\n              <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault1\" [value]=\"true\"\r\n                [(ngModel)]=\"isStatus\">\r\n              <label class=\"form-check-label\" for=\"flexRadioDefault1\">\r\n                依開放時段顯示\r\n              </label>\r\n            </div>\r\n            <div class=\"form-check mx-2\">\r\n              <input class=\"form-check-input\" type=\"radio\" name=\"flexRadioDefault\" id=\"flexRadioDefault2\"\r\n                [value]=\"false\" [(ngModel)]=\"isStatus\">\r\n              <label class=\"form-check-label\" for=\"flexRadioDefault2\">\r\n                依開放狀態顯示\r\n              </label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"col-md-5 max-sm:col-md-12 \">\r\n          <div class=\"d-flex justify-content-end w-full\">\r\n            <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"getHouseChangeDate()\">\r\n              查詢 <i class=\"fas fa-search\"></i>\r\n            </button>\r\n            <button class=\"btn btn-info btn-sm\" (click)=\"openBatchSetting()\">\r\n              全部批次設定\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 增強的搜尋和篩選區域 -->\r\n    <div class=\"search-enhanced mt-3\" *ngIf=\"flattenedHouses.length > 0\">\r\n      <div class=\"row align-items-center\">\r\n        <div class=\"col-md-3\">\r\n          <nb-form-field>\r\n            <nb-icon nbPrefix icon=\"search-outline\"></nb-icon>\r\n            <input nbInput placeholder=\"搜尋戶型、棟別...\" [(ngModel)]=\"filterOptions.searchKeyword\"\r\n              (ngModelChange)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n        <div class=\"col-md-2\">\r\n          <nb-select placeholder=\"狀態篩選\" [(ngModel)]=\"filterOptions.statusFilter\" (selectedChange)=\"onSearch()\">\r\n            <nb-option value=\"\">全部狀態</nb-option>\r\n            <nb-option value=\"active\">進行中</nb-option>\r\n            <nb-option value=\"pending\">待開放</nb-option>\r\n            <nb-option value=\"expired\">已過期</nb-option>\r\n            <nb-option value=\"not-set\">未設定</nb-option>\r\n            <nb-option value=\"disabled\">已停用</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"col-md-2\">\r\n          <nb-select placeholder=\"樓層篩選\" [(ngModel)]=\"filterOptions.floorFilter\" (selectedChange)=\"onSearch()\">\r\n            <nb-option value=\"\">全部樓層</nb-option>\r\n            <nb-option *ngFor=\"let floor of availableFloors\" [value]=\"floor\">\r\n              {{ floor }}F\r\n            </nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"col-md-2\">\r\n          <nb-select placeholder=\"顯示筆數\" [(ngModel)]=\"pageSize\" (selectedChange)=\"onPageSizeChange()\">\r\n            <nb-option [value]=\"50\">50筆/頁</nb-option>\r\n            <nb-option [value]=\"100\">100筆/頁</nb-option>\r\n            <nb-option [value]=\"200\">200筆/頁</nb-option>\r\n            <nb-option [value]=\"500\">500筆/頁</nb-option>\r\n          </nb-select>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <div class=\"d-flex justify-content-between align-items-center\">\r\n            <div class=\"text-muted small\">\r\n              共 {{ filteredHouses.length }} 筆資料\r\n              <span *ngIf=\"selectedHouses.length > 0\" class=\"text-primary\">\r\n                (已選 {{ selectedHouses.length }} 筆)\r\n              </span>\r\n            </div>\r\n            <div class=\"view-toggle\">\r\n              <button class=\"btn btn-sm\" [class.btn-primary]=\"viewMode === 'table'\"\r\n                [class.btn-outline-primary]=\"viewMode !== 'table'\" (click)=\"setViewMode('table')\">\r\n                <i class=\"fas fa-table\"></i> 表格\r\n              </button>\r\n              <button class=\"btn btn-sm ml-1\" [class.btn-primary]=\"viewMode === 'card'\"\r\n                [class.btn-outline-primary]=\"viewMode !== 'card'\" (click)=\"setViewMode('card')\">\r\n                <i class=\"fas fa-th\"></i> 卡片\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 表格視圖 -->\r\n    <div class=\"table-view mt-4\" *ngIf=\"viewMode === 'table' && flattenedHouses.length > 0\">\r\n      <!-- 工具列 -->\r\n      <div class=\"table-toolbar mb-3\">\r\n        <div class=\"d-flex justify-content-between align-items-center\">\r\n          <div class=\"batch-actions\">\r\n            <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\">\r\n              全選\r\n            </nb-checkbox>\r\n            <button class=\"btn btn-sm btn-primary ml-2\" [disabled]=\"selectedHouses.length === 0\"\r\n              (click)=\"openBatchSetting()\">\r\n              <i class=\"fas fa-edit\"></i> 批次設定 ({{ selectedHouses.length }})\r\n            </button>\r\n            <button class=\"btn btn-sm btn-success ml-2\" [disabled]=\"filteredHouses.length === 0\" (click)=\"exportData()\">\r\n              <i class=\"fas fa-download\"></i> 匯出\r\n            </button>\r\n          </div>\r\n          <div class=\"pagination-info\">\r\n            顯示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredHouses.length) }}\r\n            / 共 {{ filteredHouses.length }} 筆\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 表格 -->\r\n      <div class=\"table-container\">\r\n        <table class=\"table table-hover\">\r\n          <thead class=\"table-header\">\r\n            <tr>\r\n              <th width=\"50\">\r\n                <nb-checkbox [(ngModel)]=\"selectAll\" (ngModelChange)=\"onSelectAllChange()\"></nb-checkbox>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CHouseHold')\" class=\"sortable\">\r\n                戶型\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CHouseHold' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CHouseHold' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\" (click)=\"sort('CBuildingName')\" class=\"sortable\">\r\n                棟別\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CBuildingName' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CBuildingName' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"80\" (click)=\"sort('CFloor')\" class=\"sortable\">\r\n                樓層\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CFloor' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CFloor' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeStartDate')\" class=\"sortable\">\r\n                開始日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeStartDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeStartDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"120\" (click)=\"sort('CChangeEndDate')\" class=\"sortable\">\r\n                結束日期\r\n                <i class=\"fas fa-sort\" [class.fa-sort-up]=\"sortField === 'CChangeEndDate' && sortDirection === 'asc'\"\r\n                  [class.fa-sort-down]=\"sortField === 'CChangeEndDate' && sortDirection === 'desc'\"></i>\r\n              </th>\r\n              <th width=\"100\">狀態</th>\r\n              <th width=\"80\">操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\"\r\n              [class.table-row-selected]=\"house.selected\">\r\n              <td>\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </td>\r\n              <td>{{ house.CHouseHold }}</td>\r\n              <td>{{ house.CBuildingName }}</td>\r\n              <td>{{ house.CFloor }}F</td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeStartDate\" class=\"date-display\">\r\n                  {{ house.CChangeStartDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeStartDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span *ngIf=\"house.CChangeEndDate\" class=\"date-display\">\r\n                  {{ house.CChangeEndDate | date:'yyyy-MM-dd' }}\r\n                </span>\r\n                <span *ngIf=\"!house.CChangeEndDate\" class=\"text-muted\">未設定</span>\r\n              </td>\r\n              <td>\r\n                <span class=\"status-badge\" [class]=\"getStatusClass(house)\">\r\n                  {{ getStatusText(house) }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                  (click)=\"openModel(dialog, house)\">\r\n                  <i class=\"fas fa-edit\"></i>\r\n                </button>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n\r\n      <!-- 分頁控制 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"totalPages > 1\">\r\n        <nav>\r\n          <ul class=\"pagination justify-content-center\">\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">首頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">上一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\"\r\n                [disabled]=\"currentPage === totalPages\">下一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(totalPages)\"\r\n                [disabled]=\"currentPage === totalPages\">末頁</button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 優化的卡片視圖 -->\r\n    <div class=\"card-view mt-4\" *ngIf=\"viewMode === 'card' && flattenedHouses.length > 0\">\r\n      <div class=\"row\">\r\n        <div class=\"col-lg-3 col-md-4 col-sm-6 mb-3\" *ngFor=\"let house of paginatedHouses; trackBy: trackByHouseId\">\r\n          <nb-card class=\"house-card\" [class.selected]=\"house.selected\" [class.disabled]=\"!house.CHouseId\">\r\n            <nb-card-body class=\"p-3\">\r\n              <div class=\"d-flex justify-content-between align-items-start mb-2\">\r\n                <div class=\"house-info\">\r\n                  <h6 class=\"house-number mb-1\">{{ house.CHouseHold }}</h6>\r\n                  <small class=\"text-muted\">{{ house.CBuildingName }} - {{ house.CFloor }}F</small>\r\n                </div>\r\n                <nb-checkbox [(ngModel)]=\"house.selected\" [disabled]=\"!house.CHouseId\"\r\n                  (ngModelChange)=\"onHouseSelectionChange()\"></nb-checkbox>\r\n              </div>\r\n\r\n              <div class=\"date-info mb-2\">\r\n                <div class=\"date-row\">\r\n                  <small class=\"text-muted\">開始：</small>\r\n                  <span *ngIf=\"house.CChangeStartDate\" class=\"date-value\">\r\n                    {{ house.CChangeStartDate | date:'MM/dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeStartDate\" class=\"text-muted\">未設定</span>\r\n                </div>\r\n                <div class=\"date-row\">\r\n                  <small class=\"text-muted\">結束：</small>\r\n                  <span *ngIf=\"house.CChangeEndDate\" class=\"date-value\">\r\n                    {{ house.CChangeEndDate | date:'MM/dd' }}\r\n                  </span>\r\n                  <span *ngIf=\"!house.CChangeEndDate\" class=\"text-muted\">未設定</span>\r\n                </div>\r\n              </div>\r\n\r\n              <div class=\"d-flex justify-content-between align-items-center\">\r\n                <span class=\"status-badge small\" [class]=\"getStatusClass(house)\">\r\n                  {{ getStatusText(house) }}\r\n                </span>\r\n                <button class=\"btn btn-sm btn-outline-primary\" [disabled]=\"!house.CHouseId\"\r\n                  (click)=\"openModel(dialog, house)\">\r\n                  <i class=\"fas fa-edit\"></i>\r\n                </button>\r\n              </div>\r\n            </nb-card-body>\r\n          </nb-card>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 分頁控制 -->\r\n      <div class=\"pagination-container mt-3\" *ngIf=\"totalPages > 1\">\r\n        <nav>\r\n          <ul class=\"pagination justify-content-center\">\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(1)\" [disabled]=\"currentPage === 1\">首頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === 1\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage - 1)\" [disabled]=\"currentPage === 1\">上一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" *ngFor=\"let page of getVisiblePages()\" [class.active]=\"page === currentPage\">\r\n              <button class=\"page-link\" (click)=\"goToPage(page)\">{{ page }}</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(currentPage + 1)\"\r\n                [disabled]=\"currentPage === totalPages\">下一頁</button>\r\n            </li>\r\n            <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages\">\r\n              <button class=\"page-link\" (click)=\"goToPage(totalPages)\"\r\n                [disabled]=\"currentPage === totalPages\">末頁</button>\r\n            </li>\r\n          </ul>\r\n        </nav>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 當沒有資料時顯示 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"flattenedHouses.length === 0 && houseChangeDates.length === 0\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <i class=\"fas fa-info-circle fa-2x mb-3\"></i>\r\n            <p>請選擇建案後查詢資料</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n\r\n    <!-- 載入中狀態 -->\r\n    <div class=\"text-center mt-4\" *ngIf=\"loading\">\r\n      <nb-card>\r\n        <nb-card-body>\r\n          <div class=\"text-muted\">\r\n            <div class=\"spinner-border\" role=\"status\">\r\n              <span class=\"sr-only\">載入中...</span>\r\n            </div>\r\n            <p class=\"mt-2\">資料載入中，請稍候...</p>\r\n          </div>\r\n        </nb-card-body>\r\n      </nb-card>\r\n    </div>\r\n  </nb-card-body>\r\n</nb-card>\r\n\r\n<!-- 批次設定對話框 -->\r\n<ng-template #batchSettingDialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:600px; max-height: 90vh\">\r\n    <nb-card-header>\r\n      批次設定 - {{ selectedBuildingForBatch?.name || '全部' }}\r\n    </nb-card-header>\r\n    <nb-card-body>\r\n      <!-- 時間設定 -->\r\n      <div class=\"form-group\">\r\n        <label>開放時間起訖 <span class=\"text-red-600\">*</span></label>\r\n        <div class=\"d-flex align-items-center\">\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchStartDate\"\r\n              [(ngModel)]=\"batchSettings.startDate\">\r\n            <nb-datepicker #batchStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n          <span class=\"mx-2\">~</span>\r\n          <nb-form-field class=\"flex-fill\">\r\n            <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n            <input nbInput type=\"text\" placeholder=\"年/月/日\" [nbDatepicker]=\"batchEndDate\"\r\n              [(ngModel)]=\"batchSettings.endDate\">\r\n            <nb-datepicker #batchEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 選擇範圍 -->\r\n      <div class=\"form-group\">\r\n        <label>適用範圍</label>\r\n        <div class=\"selection-options\">\r\n          <nb-checkbox [(ngModel)]=\"batchSettings.applyToAll\">\r\n            全部戶別\r\n          </nb-checkbox>\r\n          <div class=\"mt-2\" *ngIf=\"!batchSettings.applyToAll && selectedBuildingForBatch\">\r\n            <div class=\"floor-selection\" *ngFor=\"let floor of selectedBuildingForBatch.floors\">\r\n              <nb-checkbox [(ngModel)]=\"floor.selected\" (ngModelChange)=\"onFloorSelectionChange(floor)\">\r\n                {{ floor.floorNumber }}F ({{ floor.houses.length }} 戶)\r\n              </nb-checkbox>\r\n              <div class=\"house-selection ml-4\" *ngIf=\"floor.selected\">\r\n                <nb-checkbox *ngFor=\"let house of floor.houses\" [(ngModel)]=\"house.selected\"\r\n                  [disabled]=\"!house.CHouseId\">\r\n                  {{ house.CHouseHold }} ({{ house.CBuildingName }})\r\n                </nb-checkbox>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-end\">\r\n      <button class=\"btn btn-secondary me-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary\" (click)=\"onBatchSubmit(ref)\">批次設定</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialogUpdateHousehold let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n<ng-template #dialog let-dialog let-ref=\"dialogRef\">\r\n  <nb-card style=\"width:500px; max-height: 95vh\">\r\n    <nb-card-header>\r\n      您正在編輯 <span class=\"text-green-600\">{{selectedHouseChangeDate.CHouseHold}} -\r\n        {{selectedHouseChangeDate.CFloor}}F</span> 的選樣開放時段\r\n    </nb-card-header>\r\n    <nb-card-body class=\"px-4\">\r\n      <div class=\"form-group d-flex align-items-center\">\r\n        <label for=\"cFloorFrom\" class=\"label col-3\">開放時間起訖<span class=\"text-red-600\">*</span>\r\n        </label>\r\n        <nb-form-field class=\"ml-3\">\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeStartDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeStartDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeStartDate\">\r\n          <nb-datepicker #changeStartDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n        <label for=\"CChangeStartDate\" class=\"label col-1\"> ~\r\n        </label>\r\n        <nb-form-field>\r\n          <nb-icon nbPrefix icon=\"calendar-outline\"></nb-icon>\r\n          <input nbInput type=\"text\" id=\"CChangeEndDate\" placeholder=\"年/月/日\" [nbDatepicker]=\"changeEndDate\"\r\n            class=\"w-full col-4\" [(ngModel)]=\"selectedHouseChangeDate.CChangeEndDate\">\r\n          <nb-datepicker #changeEndDate format=\"yyyy-MM-dd\"></nb-datepicker>\r\n        </nb-form-field>\r\n      </div>\r\n    </nb-card-body>\r\n    <nb-card-footer class=\"d-flex justify-content-center\">\r\n      <button class=\"btn btn-secondary btn-sm mx-2\" (click)=\"onClose(ref)\">取消</button>\r\n      <button class=\"btn btn-primary btn-sm mx-2\" (click)=\"onSubmit(ref)\">儲存</button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,GAAG,QAAQ,MAAM;AAC1B,SAA0BC,kBAAkB,QAAQ,gBAAgB;AACpE,SAASC,mBAAmB,QAAQ,mBAAmB;AACvD,SAASC,iBAAiB,QAAQ,qBAAqB;AAQvD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SAASC,MAAM,QAA8B,uCAAuC;;;;;;;;;;;;;;;;ICDtEC,EAAA,CAAAC,cAAA,oBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACjEL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,cAAA,MACF;;;;;IAUAR,EAAA,CAAAC,cAAA,oBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFwCH,EAAA,CAAAI,UAAA,UAAAK,WAAA,CAAkB;IACpET,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAE,WAAA,MACF;;;;;IAiFFT,EAAA,CAAAC,cAAA,oBAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IAFqCH,EAAA,CAAAI,UAAA,UAAAM,QAAA,CAAe;IAC9DV,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAG,QAAA,OACF;;;;;IAeEV,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,oBAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,cACF;;;;;;IAtCJb,EAHN,CAAAC,cAAA,cAAqE,cAC/B,cACZ,oBACL;IACbD,EAAA,CAAAc,SAAA,kBAAkD;IAClDd,EAAA,CAAAC,cAAA,gBAC+B;IADSD,EAAA,CAAAe,gBAAA,2BAAAC,0EAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,CAAAC,aAAA,EAAAN,MAAA,MAAAN,MAAA,CAAAW,aAAA,CAAAC,aAAA,GAAAN,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAyC;IAC/EjB,EAAA,CAAAyB,UAAA,2BAAAT,0EAAA;MAAAhB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAAe,QAAA,EAAU;IAAA,EAAC;IAElC1B,EAHI,CAAAG,YAAA,EAC+B,EACjB,EACZ;IAEJH,EADF,CAAAC,cAAA,cAAsB,oBACiF;IAAvED,EAAA,CAAAe,gBAAA,2BAAAY,8EAAAV,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,CAAAM,YAAA,EAAAX,MAAA,MAAAN,MAAA,CAAAW,aAAA,CAAAM,YAAA,GAAAX,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAwC;IAACjB,EAAA,CAAAyB,UAAA,4BAAAI,+EAAA;MAAA7B,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAkBb,MAAA,CAAAe,QAAA,EAAU;IAAA,EAAC;IAClG1B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,+BAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAC,cAAA,qBAA0B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA2B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAC,cAAA,qBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAEJH,EADF,CAAAC,cAAA,eAAsB,qBACgF;IAAtED,EAAA,CAAAe,gBAAA,2BAAAe,+EAAAb,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAW,aAAA,CAAAS,WAAA,EAAAd,MAAA,MAAAN,MAAA,CAAAW,aAAA,CAAAS,WAAA,GAAAd,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAuC;IAACjB,EAAA,CAAAyB,UAAA,4BAAAO,gFAAA;MAAAhC,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAkBb,MAAA,CAAAe,QAAA,EAAU;IAAA,EAAC;IACjG1B,EAAA,CAAAC,cAAA,qBAAoB;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACpCH,EAAA,CAAAiC,UAAA,KAAAC,uDAAA,wBAAiE;IAIrElC,EADE,CAAAG,YAAA,EAAY,EACR;IAEJH,EADF,CAAAC,cAAA,eAAsB,qBACuE;IAA7DD,EAAA,CAAAe,gBAAA,2BAAAoB,+EAAAlB,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAyB,QAAA,EAAAnB,MAAA,MAAAN,MAAA,CAAAyB,QAAA,GAAAnB,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAsB;IAACjB,EAAA,CAAAyB,UAAA,4BAAAY,gFAAA;MAAArC,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAkBb,MAAA,CAAA2B,gBAAA,EAAkB;IAAA,EAAC;IACxFtC,EAAA,CAAAC,cAAA,qBAAwB;IAAAD,EAAA,CAAAE,MAAA,uBAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACzCH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC3CH,EAAA,CAAAC,cAAA,qBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAM;IAEnCF,EAFmC,CAAAG,YAAA,EAAY,EACjC,EACR;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eAC2C,eAC/B;IAC5BD,EAAA,CAAAE,MAAA,IACA;IAAAF,EAAA,CAAAiC,UAAA,KAAAM,kDAAA,mBAA6D;IAG/DvC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,kBAE6D;IAA/BD,EAAA,CAAAyB,UAAA,mBAAAe,oEAAA;MAAAxC,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA8B,WAAA,CAAY,OAAO,CAAC;IAAA,EAAC;IACjFzC,EAAA,CAAAc,SAAA,aAA4B;IAACd,EAAA,CAAAE,MAAA,sBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACkF;IAA9BD,EAAA,CAAAyB,UAAA,mBAAAiB,oEAAA;MAAA1C,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA8B,WAAA,CAAY,MAAM,CAAC;IAAA,EAAC;IAC/EzC,EAAA,CAAAc,SAAA,aAAyB;IAACd,EAAA,CAAAE,MAAA,sBAC5B;IAKVF,EALU,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF,EACF;;;;IAnD0CH,EAAA,CAAAM,SAAA,GAAyC;IAAzCN,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAW,aAAA,CAAAC,aAAA,CAAyC;IAKrDvB,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAW,aAAA,CAAAM,YAAA,CAAwC;IAUxC5B,EAAA,CAAAM,SAAA,IAAuC;IAAvCN,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAW,aAAA,CAAAS,WAAA,CAAuC;IAEtC/B,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAiC,eAAA,CAAkB;IAMnB5C,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAyB,QAAA,CAAsB;IACvCpC,EAAA,CAAAM,SAAA,EAAY;IAAZN,EAAA,CAAAI,UAAA,aAAY;IACZJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IACbJ,EAAA,CAAAM,SAAA,GAAa;IAAbN,EAAA,CAAAI,UAAA,cAAa;IAMtBJ,EAAA,CAAAM,SAAA,GACA;IADAN,EAAA,CAAAO,kBAAA,aAAAI,MAAA,CAAAkC,cAAA,CAAAhC,MAAA,yBACA;IAAOb,EAAA,CAAAM,SAAA,EAA+B;IAA/BN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAA+B;IAKXb,EAAA,CAAAM,SAAA,GAA0C;IACnEN,EADyB,CAAA8C,WAAA,gBAAAnC,MAAA,CAAAoC,QAAA,aAA0C,wBAAApC,MAAA,CAAAoC,QAAA,aACjB;IAGpB/C,EAAA,CAAAM,SAAA,GAAyC;IACvEN,EAD8B,CAAA8C,WAAA,gBAAAnC,MAAA,CAAAoC,QAAA,YAAyC,wBAAApC,MAAA,CAAAoC,QAAA,YACtB;;;;;IAiFjD/C,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgD,WAAA,OAAAC,QAAA,CAAAC,gBAAA,qBACF;;;;;IACAlD,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGnEH,EAAA,CAAAC,cAAA,eAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgD,WAAA,OAAAC,QAAA,CAAAE,cAAA,qBACF;;;;;IACAnD,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAhBjEH,EAHJ,CAAAC,cAAA,SAC8C,SACxC,sBAE2C;IADhCD,EAAA,CAAAe,gBAAA,2BAAAqC,sFAAAnC,MAAA;MAAA,MAAAgC,QAAA,GAAAjD,EAAA,CAAAkB,aAAA,CAAAmC,GAAA,EAAAC,SAAA;MAAAtD,EAAA,CAAAqB,kBAAA,CAAA4B,QAAA,CAAAM,QAAA,EAAAtC,MAAA,MAAAgC,QAAA,CAAAM,QAAA,GAAAtC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAA4B;IACvCjB,EAAA,CAAAyB,UAAA,2BAAA2B,sFAAA;MAAApD,EAAA,CAAAkB,aAAA,CAAAmC,GAAA;MAAA,MAAA1C,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA6C,sBAAA,EAAwB;IAAA,EAAC;IAC9CxD,EAD+C,CAAAG,YAAA,EAAc,EACxD;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAI;IAIFD,EAHA,CAAAiC,UAAA,KAAAwB,wDAAA,mBAA0D,KAAAC,wDAAA,mBAGD;IAC3D1D,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,UAAI;IAIFD,EAHA,CAAAiC,UAAA,KAAA0B,wDAAA,mBAAwD,KAAAC,wDAAA,mBAGD;IACzD5D,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACyD;IACzDD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,UAAI,kBAEmC;IAAnCD,EAAA,CAAAyB,UAAA,mBAAAoC,0EAAA;MAAA,MAAAZ,QAAA,GAAAjD,EAAA,CAAAkB,aAAA,CAAAmC,GAAA,EAAAC,SAAA;MAAA,MAAA3C,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,MAAA0C,UAAA,GAAA9D,EAAA,CAAA+D,WAAA;MAAA,OAAA/D,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAqD,SAAA,CAAAF,UAAA,EAAAb,QAAA,CAAwB;IAAA,EAAC;IAClCjD,EAAA,CAAAc,SAAA,aAA2B;IAGjCd,EAFI,CAAAG,YAAA,EAAS,EACN,EACF;;;;;IA/BHH,EAAA,CAAA8C,WAAA,uBAAAG,QAAA,CAAAM,QAAA,CAA2C;IAE5BvD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAA2C,gBAAA,YAAAM,QAAA,CAAAM,QAAA,CAA4B;IAACvD,EAAA,CAAAI,UAAA,cAAA6C,QAAA,CAAAgB,QAAA,CAA4B;IAGpEjE,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAkE,iBAAA,CAAAjB,QAAA,CAAAkB,UAAA,CAAsB;IACtBnE,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAkE,iBAAA,CAAAjB,QAAA,CAAAmB,aAAA,CAAyB;IACzBpE,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAO,kBAAA,KAAA0C,QAAA,CAAAoB,MAAA,MAAmB;IAEdrE,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAA6C,QAAA,CAAAC,gBAAA,CAA4B;IAG5BlD,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAA6C,QAAA,CAAAC,gBAAA,CAA6B;IAG7BlD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAA6C,QAAA,CAAAE,cAAA,CAA0B;IAG1BnD,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAA6C,QAAA,CAAAE,cAAA,CAA2B;IAGPnD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAsE,UAAA,CAAA3D,MAAA,CAAA4D,cAAA,CAAAtB,QAAA,EAA+B;IACxDjD,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAA6D,aAAA,CAAAvB,QAAA,OACF;IAG+CjD,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAA6C,QAAA,CAAAgB,QAAA,CAA4B;;;;;;IAqB7EjE,EADF,CAAAC,cAAA,aAAmG,kBAC9C;IAAzBD,EAAA,CAAAyB,UAAA,mBAAAgD,+EAAA;MAAA,MAAAC,QAAA,GAAA1E,EAAA,CAAAkB,aAAA,CAAAyD,IAAA,EAAArB,SAAA;MAAA,MAAA3C,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAAF,QAAA,CAAc;IAAA,EAAC;IAAC1E,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAA8C,WAAA,WAAA4B,QAAA,KAAA/D,MAAA,CAAAkE,WAAA,CAAqC;IAC7C7E,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAkE,iBAAA,CAAAQ,QAAA,CAAU;;;;;;IAN7D1E,EAJR,CAAAC,cAAA,cAA8D,UACvD,aAC2C,aACe,kBACsB;IAArDD,EAAA,CAAAyB,UAAA,mBAAAqD,0EAAA;MAAA9E,EAAA,CAAAkB,aAAA,CAAA6D,IAAA;MAAA,MAAApE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAAgC5E,EAAA,CAAAE,MAAA,mBAAE;IACnFF,EADmF,CAAAG,YAAA,EAAS,EACvF;IAEHH,EADF,CAAAC,cAAA,aAA2D,kBACoC;IAAnED,EAAA,CAAAyB,UAAA,mBAAAuD,0EAAA;MAAAhF,EAAA,CAAAkB,aAAA,CAAA6D,IAAA;MAAA,MAAApE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAAjE,MAAA,CAAAkE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAAgC7E,EAAA,CAAAE,MAAA,yBAAG;IAClGF,EADkG,CAAAG,YAAA,EAAS,EACtG;IACLH,EAAA,CAAAiC,UAAA,IAAAgD,sDAAA,kBAAmG;IAIjGjF,EADF,CAAAC,cAAA,cAAoE,mBAExB;IADhBD,EAAA,CAAAyB,UAAA,mBAAAyD,2EAAA;MAAAlF,EAAA,CAAAkB,aAAA,CAAA6D,IAAA;MAAA,MAAApE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAAjE,MAAA,CAAAkE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IACnB7E,EAAA,CAAAE,MAAA,0BAAG;IAC/CF,EAD+C,CAAAG,YAAA,EAAS,EACnD;IAEHH,EADF,CAAAC,cAAA,cAAoE,mBAExB;IADhBD,EAAA,CAAAyB,UAAA,mBAAA0D,2EAAA;MAAAnF,EAAA,CAAAkB,aAAA,CAAA6D,IAAA;MAAA,MAAApE,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAAjE,MAAA,CAAAyE,UAAA,CAAoB;IAAA,EAAC;IACdpF,EAAA,CAAAE,MAAA,oBAAE;IAIpDF,EAJoD,CAAAG,YAAA,EAAS,EAClD,EACF,EACD,EACF;;;;IAnBsBH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,OAAoC;IACR7E,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,OAA8B;IAE1D7E,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,OAAoC;IACM7E,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,OAA8B;IAEvD7E,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA0E,eAAA,GAAoB;IAGrCrF,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAA6C;IAE/DpF,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAAuC;IAErBpF,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAA6C;IAE/DpF,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAAuC;;;;;;IAhH3CpF,EALR,CAAAC,cAAA,cAAwF,cAEtD,cACiC,cAClC,sBACkD;IAA9DD,EAAA,CAAAe,gBAAA,2BAAAuE,gFAAArE,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAA6E,SAAA,EAAAvE,MAAA,MAAAN,MAAA,CAAA6E,SAAA,GAAAvE,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAuB;IAACjB,EAAA,CAAAyB,UAAA,2BAAA6D,gFAAA;MAAAtF,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IACxEzF,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAC,cAAA,iBAC+B;IAA7BD,EAAA,CAAAyB,UAAA,mBAAAiE,mEAAA;MAAA1F,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAgF,gBAAA,EAAkB;IAAA,EAAC;IAC5B3F,EAAA,CAAAc,SAAA,YAA2B;IAACd,EAAA,CAAAE,MAAA,GAC9B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAA4G;IAAvBD,EAAA,CAAAyB,UAAA,mBAAAmE,mEAAA;MAAA5F,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAkF,UAAA,EAAY;IAAA,EAAC;IACzG7F,EAAA,CAAAc,SAAA,aAA+B;IAACd,EAAA,CAAAE,MAAA,sBAClC;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAE,MAAA,IAEF;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAQIH,EALV,CAAAC,cAAA,eAA6B,iBACM,iBACH,UACtB,cACa,uBAC8D;IAA9DD,EAAA,CAAAe,gBAAA,2BAAA+E,iFAAA7E,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAA6E,SAAA,EAAAvE,MAAA,MAAAN,MAAA,CAAA6E,SAAA,GAAAvE,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAuB;IAACjB,EAAA,CAAAyB,UAAA,2BAAAqE,iFAAA;MAAA9F,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA8E,iBAAA,EAAmB;IAAA,EAAC;IAC5EzF,EAD6E,CAAAG,YAAA,EAAc,EACtF;IACLH,EAAA,CAAAC,cAAA,cAA8D;IAA9CD,EAAA,CAAAyB,UAAA,mBAAAsE,gEAAA;MAAA/F,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAqF,IAAA,CAAK,YAAY,CAAC;IAAA,EAAC;IAC1ChG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAc,SAAA,aACoF;IACtFd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiE;IAAjDD,EAAA,CAAAyB,UAAA,mBAAAwE,gEAAA;MAAAjG,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAqF,IAAA,CAAK,eAAe,CAAC;IAAA,EAAC;IAC7ChG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAc,SAAA,aACuF;IACzFd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAyD;IAA1CD,EAAA,CAAAyB,UAAA,mBAAAyE,gEAAA;MAAAlG,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAqF,IAAA,CAAK,QAAQ,CAAC;IAAA,EAAC;IACrChG,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAc,SAAA,aACgF;IAClFd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAoE;IAApDD,EAAA,CAAAyB,UAAA,mBAAA0E,gEAAA;MAAAnG,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAqF,IAAA,CAAK,kBAAkB,CAAC;IAAA,EAAC;IAChDhG,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAc,SAAA,aAC0F;IAC5Fd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAkE;IAAlDD,EAAA,CAAAyB,UAAA,mBAAA2E,gEAAA;MAAApG,EAAA,CAAAkB,aAAA,CAAAqE,GAAA;MAAA,MAAA5E,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAqF,IAAA,CAAK,gBAAgB,CAAC;IAAA,EAAC;IAC9ChG,EAAA,CAAAE,MAAA,kCACA;IAAAF,EAAA,CAAAc,SAAA,aACwF;IAC1Fd,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAAe;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAErBF,EAFqB,CAAAG,YAAA,EAAK,EACnB,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAiC,UAAA,KAAAoE,gDAAA,mBAC8C;IAkCpDrG,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;IAGNH,EAAA,CAAAiC,UAAA,KAAAqE,iDAAA,oBAA8D;IAuBhEtG,EAAA,CAAAG,YAAA,EAAM;;;;IArHeH,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAA6E,SAAA,CAAuB;IAGQxF,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAAwC;IAEtDb,EAAA,CAAAM,SAAA,GAC9B;IAD8BN,EAAA,CAAAO,kBAAA,gCAAAI,MAAA,CAAAC,cAAA,CAAAC,MAAA,OAC9B;IAC4Cb,EAAA,CAAAM,SAAA,EAAwC;IAAxCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkC,cAAA,CAAAhC,MAAA,OAAwC;IAKpFb,EAAA,CAAAM,SAAA,GAEF;IAFEN,EAAA,CAAAuG,kBAAA,oBAAA5F,MAAA,CAAAkE,WAAA,QAAAlE,MAAA,CAAAyB,QAAA,aAAAzB,MAAA,CAAA6F,IAAA,CAAAC,GAAA,CAAA9F,MAAA,CAAAkE,WAAA,GAAAlE,MAAA,CAAAyB,QAAA,EAAAzB,MAAA,CAAAkC,cAAA,CAAAhC,MAAA,iBAAAF,MAAA,CAAAkC,cAAA,CAAAhC,MAAA,aAEF;IAUmBb,EAAA,CAAAM,SAAA,GAAuB;IAAvBN,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAA6E,SAAA,CAAuB;IAIbxF,EAAA,CAAAM,SAAA,GAA0E;IAC/FN,EADqB,CAAA8C,WAAA,eAAAnC,MAAA,CAAA+F,SAAA,qBAAA/F,MAAA,CAAAgG,aAAA,WAA0E,iBAAAhG,MAAA,CAAA+F,SAAA,qBAAA/F,MAAA,CAAAgG,aAAA,YAClB;IAIxD3G,EAAA,CAAAM,SAAA,GAA6E;IAClGN,EADqB,CAAA8C,WAAA,eAAAnC,MAAA,CAAA+F,SAAA,wBAAA/F,MAAA,CAAAgG,aAAA,WAA6E,iBAAAhG,MAAA,CAAA+F,SAAA,wBAAA/F,MAAA,CAAAgG,aAAA,YAClB;IAI3D3G,EAAA,CAAAM,SAAA,GAAsE;IAC3FN,EADqB,CAAA8C,WAAA,eAAAnC,MAAA,CAAA+F,SAAA,iBAAA/F,MAAA,CAAAgG,aAAA,WAAsE,iBAAAhG,MAAA,CAAA+F,SAAA,iBAAA/F,MAAA,CAAAgG,aAAA,YAClB;IAIpD3G,EAAA,CAAAM,SAAA,GAAgF;IACrGN,EADqB,CAAA8C,WAAA,eAAAnC,MAAA,CAAA+F,SAAA,2BAAA/F,MAAA,CAAAgG,aAAA,WAAgF,iBAAAhG,MAAA,CAAA+F,SAAA,2BAAA/F,MAAA,CAAAgG,aAAA,YAClB;IAI9D3G,EAAA,CAAAM,SAAA,GAA8E;IACnGN,EADqB,CAAA8C,WAAA,eAAAnC,MAAA,CAAA+F,SAAA,yBAAA/F,MAAA,CAAAgG,aAAA,WAA8E,iBAAAhG,MAAA,CAAA+F,SAAA,yBAAA/F,MAAA,CAAAgG,aAAA,YAClB;IAOjE3G,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAO,MAAA,CAAAiG,eAAA,CAAoB,iBAAAjG,MAAA,CAAAkG,cAAA,CAAuB;IAsC/B7G,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAyE,UAAA,KAAoB;;;;;IA2ChDpF,EAAA,CAAAC,cAAA,gBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgD,WAAA,OAAA8D,SAAA,CAAA5D,gBAAA,gBACF;;;;;IACAlD,EAAA,CAAAC,cAAA,eAAyD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAInEH,EAAA,CAAAC,cAAA,gBAAsD;IACpDD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAgD,WAAA,OAAA8D,SAAA,CAAA3D,cAAA,gBACF;;;;;IACAnD,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,yBAAG;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IApBjEH,EALV,CAAAC,cAAA,eAA4G,mBACT,wBACrE,eAC2C,eACzC,cACQ;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IAC3EF,EAD2E,CAAAG,YAAA,EAAQ,EAC7E;IACNH,EAAA,CAAAC,cAAA,sBAC6C;IADhCD,EAAA,CAAAe,gBAAA,2BAAAgG,sFAAA9F,MAAA;MAAA,MAAA6F,SAAA,GAAA9G,EAAA,CAAAkB,aAAA,CAAA8F,IAAA,EAAA1D,SAAA;MAAAtD,EAAA,CAAAqB,kBAAA,CAAAyF,SAAA,CAAAvD,QAAA,EAAAtC,MAAA,MAAA6F,SAAA,CAAAvD,QAAA,GAAAtC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAA4B;IACvCjB,EAAA,CAAAyB,UAAA,2BAAAsF,sFAAA;MAAA/G,EAAA,CAAAkB,aAAA,CAAA8F,IAAA;MAAA,MAAArG,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA6C,sBAAA,EAAwB;IAAA,EAAC;IAC9CxD,EAD+C,CAAAG,YAAA,EAAc,EACvD;IAIFH,EAFJ,CAAAC,cAAA,gBAA4B,gBACJ,iBACM;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIrCH,EAHA,CAAAiC,UAAA,KAAAgF,wDAAA,oBAAwD,KAAAC,wDAAA,mBAGC;IAC3DlH,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAAsB,iBACM;IAAAD,EAAA,CAAAE,MAAA,0BAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAIrCH,EAHA,CAAAiC,UAAA,KAAAkF,wDAAA,oBAAsD,KAAAC,wDAAA,mBAGC;IAE3DpH,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA+D,iBACI;IAC/DD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,kBACqC;IAAnCD,EAAA,CAAAyB,UAAA,mBAAA4F,0EAAA;MAAA,MAAAP,SAAA,GAAA9G,EAAA,CAAAkB,aAAA,CAAA8F,IAAA,EAAA1D,SAAA;MAAA,MAAA3C,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,MAAA0C,UAAA,GAAA9D,EAAA,CAAA+D,WAAA;MAAA,OAAA/D,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAqD,SAAA,CAAAF,UAAA,EAAAgD,SAAA,CAAwB;IAAA,EAAC;IAClC9G,EAAA,CAAAc,SAAA,aAA2B;IAKrCd,EAJQ,CAAAG,YAAA,EAAS,EACL,EACO,EACP,EACN;;;;;IAvCwBH,EAAA,CAAAM,SAAA,EAAiC;IAACN,EAAlC,CAAA8C,WAAA,aAAAgE,SAAA,CAAAvD,QAAA,CAAiC,cAAAuD,SAAA,CAAA7C,QAAA,CAAmC;IAI1DjE,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAkE,iBAAA,CAAA4C,SAAA,CAAA3C,UAAA,CAAsB;IAC1BnE,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAsH,kBAAA,KAAAR,SAAA,CAAA1C,aAAA,SAAA0C,SAAA,CAAAzC,MAAA,MAA+C;IAE9DrE,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAA2C,gBAAA,YAAAmE,SAAA,CAAAvD,QAAA,CAA4B;IAACvD,EAAA,CAAAI,UAAA,cAAA0G,SAAA,CAAA7C,QAAA,CAA4B;IAO7DjE,EAAA,CAAAM,SAAA,GAA4B;IAA5BN,EAAA,CAAAI,UAAA,SAAA0G,SAAA,CAAA5D,gBAAA,CAA4B;IAG5BlD,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAI,UAAA,UAAA0G,SAAA,CAAA5D,gBAAA,CAA6B;IAI7BlD,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAA0G,SAAA,CAAA3D,cAAA,CAA0B;IAG1BnD,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAA0G,SAAA,CAAA3D,cAAA,CAA2B;IAKHnD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAsE,UAAA,CAAA3D,MAAA,CAAA4D,cAAA,CAAAuC,SAAA,EAA+B;IAC9D9G,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAI,MAAA,CAAA6D,aAAA,CAAAsC,SAAA,OACF;IAC+C9G,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAI,UAAA,cAAA0G,SAAA,CAAA7C,QAAA,CAA4B;;;;;;IAqB7EjE,EADF,CAAAC,cAAA,aAAmG,kBAC9C;IAAzBD,EAAA,CAAAyB,UAAA,mBAAA8F,8EAAA;MAAA,MAAAC,QAAA,GAAAxH,EAAA,CAAAkB,aAAA,CAAAuG,IAAA,EAAAnE,SAAA;MAAA,MAAA3C,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAA4C,QAAA,CAAc;IAAA,EAAC;IAACxH,EAAA,CAAAE,MAAA,GAAU;IAC/DF,EAD+D,CAAAG,YAAA,EAAS,EACnE;;;;;IAFwDH,EAAA,CAAA8C,WAAA,WAAA0E,QAAA,KAAA7G,MAAA,CAAAkE,WAAA,CAAqC;IAC7C7E,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAkE,iBAAA,CAAAsD,QAAA,CAAU;;;;;;IAN7DxH,EAJR,CAAAC,cAAA,cAA8D,UACvD,aAC2C,aACe,kBACsB;IAArDD,EAAA,CAAAyB,UAAA,mBAAAiG,yEAAA;MAAA1H,EAAA,CAAAkB,aAAA,CAAAyG,IAAA;MAAA,MAAAhH,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAS,CAAC,CAAC;IAAA,EAAC;IAAgC5E,EAAA,CAAAE,MAAA,mBAAE;IACnFF,EADmF,CAAAG,YAAA,EAAS,EACvF;IAEHH,EADF,CAAAC,cAAA,aAA2D,kBACoC;IAAnED,EAAA,CAAAyB,UAAA,mBAAAmG,yEAAA;MAAA5H,EAAA,CAAAkB,aAAA,CAAAyG,IAAA;MAAA,MAAAhH,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAAjE,MAAA,CAAAkE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IAAgC7E,EAAA,CAAAE,MAAA,yBAAG;IAClGF,EADkG,CAAAG,YAAA,EAAS,EACtG;IACLH,EAAA,CAAAiC,UAAA,IAAA4F,qDAAA,kBAAmG;IAIjG7H,EADF,CAAAC,cAAA,cAAoE,mBAExB;IADhBD,EAAA,CAAAyB,UAAA,mBAAAqG,0EAAA;MAAA9H,EAAA,CAAAkB,aAAA,CAAAyG,IAAA;MAAA,MAAAhH,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAAjE,MAAA,CAAAkE,WAAA,GAAuB,CAAC,CAAC;IAAA,EAAC;IACnB7E,EAAA,CAAAE,MAAA,0BAAG;IAC/CF,EAD+C,CAAAG,YAAA,EAAS,EACnD;IAEHH,EADF,CAAAC,cAAA,cAAoE,mBAExB;IADhBD,EAAA,CAAAyB,UAAA,mBAAAsG,0EAAA;MAAA/H,EAAA,CAAAkB,aAAA,CAAAyG,IAAA;MAAA,MAAAhH,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiE,QAAA,CAAAjE,MAAA,CAAAyE,UAAA,CAAoB;IAAA,EAAC;IACdpF,EAAA,CAAAE,MAAA,oBAAE;IAIpDF,EAJoD,CAAAG,YAAA,EAAS,EAClD,EACF,EACD,EACF;;;;IAnBsBH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,OAAoC;IACR7E,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,OAA8B;IAE1D7E,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,OAAoC;IACM7E,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,OAA8B;IAEvD7E,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAA0E,eAAA,GAAoB;IAGrCrF,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAA6C;IAE/DpF,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAAuC;IAErBpF,EAAA,CAAAM,SAAA,GAA6C;IAA7CN,EAAA,CAAA8C,WAAA,aAAAnC,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAA6C;IAE/DpF,EAAA,CAAAM,SAAA,EAAuC;IAAvCN,EAAA,CAAAI,UAAA,aAAAO,MAAA,CAAAkE,WAAA,KAAAlE,MAAA,CAAAyE,UAAA,CAAuC;;;;;IA/DjDpF,EADF,CAAAC,cAAA,eAAsF,eACnE;IACfD,EAAA,CAAAiC,UAAA,IAAA+F,gDAAA,qBAA4G;IAyC9GhI,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAiC,UAAA,IAAAgG,gDAAA,oBAA8D;IAuBhEjI,EAAA,CAAAG,YAAA,EAAM;;;;IAnE6DH,EAAA,CAAAM,SAAA,GAAoB;IAAAN,EAApB,CAAAI,UAAA,YAAAO,MAAA,CAAAiG,eAAA,CAAoB,iBAAAjG,MAAA,CAAAkG,cAAA,CAAuB;IA4CpE7G,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAO,MAAA,CAAAyE,UAAA,KAAoB;;;;;IA6BxDpF,EAHN,CAAAC,cAAA,eAAoG,cACzF,mBACO,cACY;IACtBD,EAAA,CAAAc,SAAA,aAA6C;IAC7Cd,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mEAAU;IAIrBF,EAJqB,CAAAG,YAAA,EAAI,EACb,EACO,EACP,EACN;;;;;IAQIH,EALV,CAAAC,cAAA,eAA8C,cACnC,mBACO,cACY,eACoB,gBAClB;IAAAD,EAAA,CAAAE,MAAA,4BAAM;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,aAAgB;IAAAD,EAAA,CAAAE,MAAA,gEAAY;IAIpCF,EAJoC,CAAAG,YAAA,EAAI,EAC5B,EACO,EACP,EACN;;;;;;IA4CMH,EAAA,CAAAC,cAAA,sBAC+B;IADiBD,EAAA,CAAAe,gBAAA,2BAAAmH,yHAAAjH,MAAA;MAAA,MAAAkH,SAAA,GAAAnI,EAAA,CAAAkB,aAAA,CAAAkH,IAAA,EAAA9E,SAAA;MAAAtD,EAAA,CAAAqB,kBAAA,CAAA8G,SAAA,CAAA5E,QAAA,EAAAtC,MAAA,MAAAkH,SAAA,CAAA5E,QAAA,GAAAtC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAE1EjB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;;;;IAHkCH,EAAA,CAAA2C,gBAAA,YAAAwF,SAAA,CAAA5E,QAAA,CAA4B;IAC1EvD,EAAA,CAAAI,UAAA,cAAA+H,SAAA,CAAAlE,QAAA,CAA4B;IAC5BjE,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAsH,kBAAA,MAAAa,SAAA,CAAAhE,UAAA,QAAAgE,SAAA,CAAA/D,aAAA,OACF;;;;;IAJFpE,EAAA,CAAAC,cAAA,eAAyD;IACvDD,EAAA,CAAAiC,UAAA,IAAAoG,mFAAA,2BAC+B;IAGjCrI,EAAA,CAAAG,YAAA,EAAM;;;;IAJ2BH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAI,UAAA,YAAAkI,SAAA,CAAAC,MAAA,CAAe;;;;;;IAJhDvI,EADF,CAAAC,cAAA,eAAmF,sBACS;IAA7ED,EAAA,CAAAe,gBAAA,2BAAAyH,qGAAAvH,MAAA;MAAA,MAAAqH,SAAA,GAAAtI,EAAA,CAAAkB,aAAA,CAAAuH,IAAA,EAAAnF,SAAA;MAAAtD,EAAA,CAAAqB,kBAAA,CAAAiH,SAAA,CAAA/E,QAAA,EAAAtC,MAAA,MAAAqH,SAAA,CAAA/E,QAAA,GAAAtC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAA4B;IAACjB,EAAA,CAAAyB,UAAA,2BAAA+G,qGAAA;MAAA,MAAAF,SAAA,GAAAtI,EAAA,CAAAkB,aAAA,CAAAuH,IAAA,EAAAnF,SAAA;MAAA,MAAA3C,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAAiBb,MAAA,CAAA+H,sBAAA,CAAAJ,SAAA,CAA6B;IAAA,EAAC;IACvFtI,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAiC,UAAA,IAAA0G,qEAAA,mBAAyD;IAM3D3I,EAAA,CAAAG,YAAA,EAAM;;;;IATSH,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAA2C,gBAAA,YAAA2F,SAAA,CAAA/E,QAAA,CAA4B;IACvCvD,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAsH,kBAAA,MAAAgB,SAAA,CAAAM,WAAA,SAAAN,SAAA,CAAAC,MAAA,CAAA1H,MAAA,cACF;IACmCb,EAAA,CAAAM,SAAA,EAAoB;IAApBN,EAAA,CAAAI,UAAA,SAAAkI,SAAA,CAAA/E,QAAA,CAAoB;;;;;IAL3DvD,EAAA,CAAAC,cAAA,eAAgF;IAC9ED,EAAA,CAAAiC,UAAA,IAAA4G,+DAAA,mBAAmF;IAWrF7I,EAAA,CAAAG,YAAA,EAAM;;;;IAX2CH,EAAA,CAAAM,SAAA,EAAkC;IAAlCN,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAmI,wBAAA,CAAAC,MAAA,CAAkC;;;;;;IAhCzF/I,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAIbH,EAHJ,CAAAC,cAAA,mBAAc,eAEY,YACf;IAAAD,EAAA,CAAAE,MAAA,4CAAO;IAAAF,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAEvDH,EADF,CAAAC,cAAA,eAAuC,0BACJ;IAC/BD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBACwC;IAAtCD,EAAA,CAAAe,gBAAA,2BAAAiI,mFAAA/H,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA+H,IAAA;MAAA,MAAAtI,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAuI,aAAA,CAAAC,SAAA,EAAAlI,MAAA,MAAAN,MAAA,CAAAuI,aAAA,CAAAC,SAAA,GAAAlI,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAqC;IADvCjB,EAAA,CAAAG,YAAA,EACwC;IACxCH,EAAA,CAAAc,SAAA,4BAAmE;IACrEd,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,iBAAmB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3BH,EAAA,CAAAC,cAAA,0BAAiC;IAC/BD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBACsC;IAApCD,EAAA,CAAAe,gBAAA,2BAAAqI,mFAAAnI,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA+H,IAAA;MAAA,MAAAtI,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAuI,aAAA,CAAAG,OAAA,EAAApI,MAAA,MAAAN,MAAA,CAAAuI,aAAA,CAAAG,OAAA,GAAApI,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAmC;IADrCjB,EAAA,CAAAG,YAAA,EACsC;IACtCH,EAAA,CAAAc,SAAA,4BAAiE;IAGvEd,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;IAIJH,EADF,CAAAC,cAAA,gBAAwB,aACf;IAAAD,EAAA,CAAAE,MAAA,gCAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEjBH,EADF,CAAAC,cAAA,gBAA+B,uBACuB;IAAvCD,EAAA,CAAAe,gBAAA,2BAAAuI,yFAAArI,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA+H,IAAA;MAAA,MAAAtI,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAuI,aAAA,CAAAK,UAAA,EAAAtI,MAAA,MAAAN,MAAA,CAAAuI,aAAA,CAAAK,UAAA,GAAAtI,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAsC;IACjDjB,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAc;IACdH,EAAA,CAAAiC,UAAA,KAAAuH,yDAAA,mBAAgF;IAetFxJ,EAFI,CAAAG,YAAA,EAAM,EACF,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAmD,mBACa;IAAvBD,EAAA,CAAAyB,UAAA,mBAAAgI,4EAAA;MAAA,MAAAC,OAAA,GAAA1J,EAAA,CAAAkB,aAAA,CAAA+H,IAAA,EAAAU,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiJ,OAAA,CAAAF,OAAA,CAAY;IAAA,EAAC;IAAC1J,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzEH,EAAA,CAAAC,cAAA,mBAA6D;IAA7BD,EAAA,CAAAyB,UAAA,mBAAAoI,4EAAA;MAAA,MAAAH,OAAA,GAAA1J,EAAA,CAAAkB,aAAA,CAAA+H,IAAA,EAAAU,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAmJ,aAAA,CAAAJ,OAAA,CAAkB;IAAA,EAAC;IAAC1J,EAAA,CAAAE,MAAA,gCAAI;IAErEF,EAFqE,CAAAG,YAAA,EAAS,EAC3D,EACT;;;;;;IAlDNH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,kCAAAI,MAAA,CAAAmI,wBAAA,kBAAAnI,MAAA,CAAAmI,wBAAA,CAAAiB,IAAA,yBACF;IAQuD/J,EAAA,CAAAM,SAAA,IAA+B;IAA/BN,EAAA,CAAAI,UAAA,iBAAA4J,kBAAA,CAA+B;IAC5EhK,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAuI,aAAA,CAAAC,SAAA,CAAqC;IAMQnJ,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAI,UAAA,iBAAA6J,gBAAA,CAA6B;IAC1EjK,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAuI,aAAA,CAAAG,OAAA,CAAmC;IAU1BrJ,EAAA,CAAAM,SAAA,GAAsC;IAAtCN,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAuI,aAAA,CAAAK,UAAA,CAAsC;IAGhCvJ,EAAA,CAAAM,SAAA,GAA2D;IAA3DN,EAAA,CAAAI,UAAA,UAAAO,MAAA,CAAAuI,aAAA,CAAAK,UAAA,IAAA5I,MAAA,CAAAmI,wBAAA,CAA2D;;;;;IAwBtF9I,EAAA,CAAAC,cAAA,mBAA+C;IAM7CD,EALA,CAAAc,SAAA,qBACiB,mBAGF,0BAGE;IACnBd,EAAA,CAAAG,YAAA,EAAU;;;;;;IAKRH,EADF,CAAAC,cAAA,mBAA+C,qBAC7B;IACdD,EAAA,CAAAE,MAAA,uCAAM;IAAAF,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,GACE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAE,MAAA,mDAC/C;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,wBAA2B,cACyB,gBACJ;IAAAD,EAAA,CAAAE,MAAA,2CAAM;IAAAF,EAAA,CAAAC,cAAA,iBAA2B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC7E;IACRH,EAAA,CAAAC,cAAA,0BAA4B;IAC1BD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBAC8E;IAAvDD,EAAA,CAAAe,gBAAA,2BAAAmJ,mFAAAjJ,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAiJ,IAAA;MAAA,MAAAxJ,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAyJ,uBAAA,CAAAlH,gBAAA,EAAAjC,MAAA,MAAAN,MAAA,CAAAyJ,uBAAA,CAAAlH,gBAAA,GAAAjC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAsD;IAD7EjB,EAAA,CAAAG,YAAA,EAC8E;IAC9EH,EAAA,CAAAc,SAAA,4BAAoE;IACtEd,EAAA,CAAAG,YAAA,EAAgB;IAChBH,EAAA,CAAAC,cAAA,kBAAkD;IAACD,EAAA,CAAAE,MAAA,WACnD;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,qBAAe;IACbD,EAAA,CAAAc,SAAA,mBAAoD;IACpDd,EAAA,CAAAC,cAAA,kBAC4E;IAArDD,EAAA,CAAAe,gBAAA,2BAAAsJ,mFAAApJ,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAiJ,IAAA;MAAA,MAAAxJ,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAApB,EAAA,CAAAqB,kBAAA,CAAAV,MAAA,CAAAyJ,uBAAA,CAAAjH,cAAA,EAAAlC,MAAA,MAAAN,MAAA,CAAAyJ,uBAAA,CAAAjH,cAAA,GAAAlC,MAAA;MAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;IAAA,EAAoD;IAD3EjB,EAAA,CAAAG,YAAA,EAC4E;IAC5EH,EAAA,CAAAc,SAAA,4BAAkE;IAGxEd,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACO;IAEbH,EADF,CAAAC,cAAA,2BAAsD,kBACiB;IAAvBD,EAAA,CAAAyB,UAAA,mBAAA6I,4EAAA;MAAA,MAAAC,OAAA,GAAAvK,EAAA,CAAAkB,aAAA,CAAAiJ,IAAA,EAAAR,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAAiJ,OAAA,CAAAW,OAAA,CAAY;IAAA,EAAC;IAACvK,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChFH,EAAA,CAAAC,cAAA,mBAAoE;IAAxBD,EAAA,CAAAyB,UAAA,mBAAA+I,4EAAA;MAAA,MAAAD,OAAA,GAAAvK,EAAA,CAAAkB,aAAA,CAAAiJ,IAAA,EAAAR,SAAA;MAAA,MAAAhJ,MAAA,GAAAX,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAwB,WAAA,CAASb,MAAA,CAAA8J,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAACvK,EAAA,CAAAE,MAAA,oBAAE;IAE1EF,EAF0E,CAAAG,YAAA,EAAS,EAChE,EACT;;;;;;IA3B6BH,EAAA,CAAAM,SAAA,GACE;IADFN,EAAA,CAAAsH,kBAAA,KAAA3G,MAAA,CAAAyJ,uBAAA,CAAAjG,UAAA,SAAAxD,MAAA,CAAAyJ,uBAAA,CAAA/F,MAAA,MACE;IAQoCrE,EAAA,CAAAM,SAAA,IAAgC;IAAhCN,EAAA,CAAAI,UAAA,iBAAAsK,mBAAA,CAAgC;IAC9E1K,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAyJ,uBAAA,CAAAlH,gBAAA,CAAsD;IAOVlD,EAAA,CAAAM,SAAA,GAA8B;IAA9BN,EAAA,CAAAI,UAAA,iBAAAuK,iBAAA,CAA8B;IAC1E3K,EAAA,CAAA2C,gBAAA,YAAAhC,MAAA,CAAAyJ,uBAAA,CAAAjH,cAAA,CAAoD;;;ADjYrF,OAAM,MAAOyH,0BAA2B,SAAQ9K,aAAa;EAK3D+K,YACUC,MAAmB,EACnBC,aAA8B,EAC9BC,OAAuB,EACvBC,KAAuB,EACvBC,aAA2B,EAC3BC,iBAAmC,EACnCC,MAAc,EACdC,aAA2B;IAGnC,KAAK,CAACP,MAAM,CAAC;IAVL,KAAAA,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IAZvB,KAAAC,eAAe,GAAW,CAAC,CAAC;IA6C5B,KAAAC,gBAAgB,GAAU,CAAC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAE,CAAE,CAAC;IAItD,KAAAC,QAAQ,GAAY,IAAI;IAOxB;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,eAAe,GAAa,EAAE;IAC9B,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAjJ,eAAe,GAAa,EAAE;IAE9B;IACA,KAAAtB,aAAa,GAAkB;MAC7BC,aAAa,EAAE,EAAE;MACjBK,YAAY,EAAE,EAAE;MAChBG,WAAW,EAAE,EAAE;MACf+J,cAAc,EAAE;KACjB;IAED;IACA,KAAA5C,aAAa,GAAkB;MAC7BC,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbE,UAAU,EAAE,IAAI;MAChBwC,iBAAiB,EAAE,EAAE;MACrBC,cAAc,EAAE,EAAE;MAClBpL,cAAc,EAAE;KACjB;IAED,KAAAkI,wBAAwB,GAAyB,IAAI;IAErD;IACA,KAAA/F,QAAQ,GAAqB,OAAO;IACpC,KAAAkJ,eAAe,GAAqB,EAAE;IACtC,KAAApJ,cAAc,GAAqB,EAAE;IACrC,KAAA+D,eAAe,GAAqB,EAAE;IACtC,KAAAhG,cAAc,GAAqB,EAAE;IACrC,KAAA4E,SAAS,GAAY,KAAK;IAC1B,KAAA0G,OAAO,GAAY,KAAK;IAExB;IACA,KAAArH,WAAW,GAAW,CAAC;IACvB,KAAAzC,QAAQ,GAAW,GAAG;IACtB,KAAAgD,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAsB,SAAS,GAAW,EAAE;IACtB,KAAAC,aAAa,GAAmB,KAAK;IAErC;IACA,KAAAH,IAAI,GAAGA,IAAI;IApFT,IAAI,CAAC4D,uBAAuB,GAAG;MAC7BlH,gBAAgB,EAAE,EAAE;MACpBC,cAAc,EAAE,EAAE;MAClBkB,MAAM,EAAE8H,SAAS;MACjBhI,UAAU,EAAE,EAAE;MACdF,QAAQ,EAAEkI;KACX;IAED,IAAI,CAACd,aAAa,CAACe,OAAO,EAAE,CAACC,IAAI,CAC/B7M,GAAG,CAAE8M,GAAW,IAAI;MAClB,IAAIA,GAAG,CAACC,MAAM,iDAA2B;QACvC,IAAI,CAACjB,eAAe,GAAGgB,GAAG,CAACE,OAAO;MACpC;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAuESC,QAAQA,CAAA;IACf,IAAI,CAACC,WAAW,GAAG;MACjBC,kBAAkB,EAAE,IAAI;MACxBC,qBAAqB,EAAE,IAAI,CAACtB,gBAAgB,CAAC,CAAC,CAAC;MAC/CrI,gBAAgB,EAAEiJ,SAAS;MAC3BhJ,cAAc,EAAEgJ;KACjB;IACD,IAAI,CAACW,gBAAgB,EAAE;EACzB;EAEA9I,SAASA,CAAC+I,GAAQ,EAAEC,IAAS;IAC3B,IAAIA,IAAI,CAAC/I,QAAQ,EAAE;MACjB,IAAI,CAACmG,uBAAuB,GAAG;QAC7B,GAAG4C,IAAI;QACP9J,gBAAgB,EAAE8J,IAAI,CAAC9J,gBAAgB,GAAG,IAAI+J,IAAI,CAACD,IAAI,CAAC9J,gBAAgB,CAAC,GAAGiJ,SAAS;QACrFhJ,cAAc,EAAE6J,IAAI,CAAC7J,cAAc,GAAG,IAAI8J,IAAI,CAACD,IAAI,CAAC7J,cAAc,CAAC,GAAGgJ;OACvE;MACD,IAAI,CAACpB,aAAa,CAACmC,IAAI,CAACH,GAAG,CAAC;IAC9B;EACF;EAEAI,UAAUA,CAACC,WAAmB;IAC5B,IAAIA,WAAW,EAAE;MACf,OAAOxN,MAAM,CAACwN,WAAW,CAAC,CAACC,MAAM,CAAC,qBAAqB,CAAC;IAC1D;IACA,OAAO,EAAE;EACX;EAEA5C,QAAQA,CAACsC,GAAQ;IACf,IAAI,CAACO,UAAU,EAAE;IACjB,IAAI,IAAI,CAACrC,KAAK,CAACsC,aAAa,CAAC1M,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACmK,OAAO,CAACwC,aAAa,CAAC,IAAI,CAACvC,KAAK,CAACsC,aAAa,CAAC;MACpD;IACF;IACA,MAAME,KAAK,GAAG;MACZxJ,QAAQ,EAAE,IAAI,CAACmG,uBAAuB,CAACnG,QAAQ;MAC/Cf,gBAAgB,EAAE,IAAI,CAACiK,UAAU,CAAC,IAAI,CAAC/C,uBAAuB,CAAClH,gBAAgB,CAAC;MAChFC,cAAc,EAAE,IAAI,CAACgK,UAAU,CAAC,IAAI,CAAC/C,uBAAuB,CAACjH,cAAc;KAC5E;IAED,IAAI,CAAC+H,aAAa,CAACwC,oCAAoC,CAAC;MACtDC,IAAI,EAAE,CAACF,KAAK;KACb,CAAC,CAAChB,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC5C,OAAO,CAAC6C,aAAa,CAAC,MAAM,CAAC;QAClC,IAAI,CAACC,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEAjB,gBAAgBA,CAAA;IACd,IAAI,CAAC3B,iBAAiB,CAAC6C,qCAAqC,CAAC;MAAEL,IAAI,EAAE;IAAE,CAAE,CAAC,CAACtB,IAAI,CAC7E7M,GAAG,CAAC8M,GAAG,IAAG;MACR,MAAM2B,OAAO,GAAG3B,GAAG,CAAC4B,OAAO,IAAI,EAAE,CAAC,CAAC;MACnC,IAAID,OAAO,CAACpN,MAAM,IAAIyL,GAAG,CAACsB,UAAU,KAAK,CAAC,EAAE;QAC1C,IAAI,CAACO,oBAAoB,GAAGF,OAAO,CAACG,GAAG,CAACC,KAAK,KAAK;UAChD7N,cAAc,EAAE6N,KAAK,CAAC7N,cAAc;UACpC8N,GAAG,EAAED,KAAK,CAACC;SACZ,CAAC,CAAC;QACH,IAAI,IAAI,CAAChD,eAAe,IAAI,CAAC,CAAC,EAAE;UAC9B,IAAIiD,KAAK,GAAG,IAAI,CAACJ,oBAAoB,CAACK,SAAS,CAAEC,CAAM,IAAKA,CAAC,CAACH,GAAG,IAAI,IAAI,CAAChD,eAAe,CAAC;UAC1F,IAAI,CAACqB,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAACI,KAAK,CAAC,IAAI,IAAI;QAChF,CAAC,MAAM;UACL,IAAI,CAAC5B,WAAW,CAACC,kBAAkB,GAAG,IAAI,CAACuB,oBAAoB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC5E;QACA,MAAMO,WAAW,GAAG,IAAI,CAAC/B,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG;QAC7D,IAAII,WAAW,EAAE;UACf,IAAI,CAACZ,kBAAkB,EAAE;QAC3B;MACF;IACF,CAAC,CAAC,CACH,CAACrB,SAAS,EAAE;EACf;EAEAkC,8BAA8BA,CAACC,GAAU;IACvC,MAAMC,SAAS,GAAwC,EAAE,CAAC,CAAC;IAC3DD,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBA,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvC,MAAMC,KAAK,GAAGD,KAAK,CAAC5K,MAAM;QAC1B,IAAI,CAACwK,SAAS,CAACK,KAAK,CAAC,EAAE;UAAE;UACvBL,SAAS,CAACK,KAAK,CAAC,GAAG,EAAE;QACvB;QACAL,SAAS,CAACK,KAAK,CAAC,CAACC,IAAI,CAAC;UACpBhL,UAAU,EAAE4K,SAAS,CAAC5K,UAAU;UAChCC,aAAa,EAAE6K,KAAK,CAAC7K,aAAa,IAAI,KAAK;UAC3CH,QAAQ,EAAEgL,KAAK,CAAChL,QAAQ;UACxBI,MAAM,EAAE4K,KAAK,CAAC5K,MAAM;UACpBnB,gBAAgB,EAAE+L,KAAK,CAAC/L,gBAAgB;UACxCC,cAAc,EAAE8L,KAAK,CAAC9L;SACvB,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC4F,MAAM,CAAC/C,IAAI,CAAC,CAACoJ,CAAM,EAAEC,CAAM,KAAKA,CAAC,GAAGD,CAAC,CAAC;IAC3C,MAAME,MAAM,GAAuB,IAAI,CAACvG,MAAM,CAACqF,GAAG,CAAEc,KAAU,IAAI;MAChE,OAAO,IAAI,CAACK,UAAU,CAACnB,GAAG,CAAEW,SAAc,IAAI;QAC5C,MAAME,KAAK,GAAGJ,SAAS,CAACK,KAAK,CAAC,CAACM,IAAI,CAAEC,CAAuB,IAAKA,CAAC,CAACtL,UAAU,KAAK4K,SAAS,CAAC;QAC5F,OAAOE,KAAK,IAAI;UACd9K,UAAU,EAAE4K,SAAS;UACrB3K,aAAa,EAAE,KAAK;UACpBH,QAAQ,EAAE,IAAI;UACdI,MAAM,EAAE6K,KAAK;UACbhM,gBAAgB,EAAE,IAAI;UACtBC,cAAc,EAAE;SACY;MAChC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOmM,MAAM;EACf;EAEAI,sBAAsBA,CAACd,GAAU;IAC/B,MAAMe,SAAS,GAAgB,IAAIC,GAAG,EAAE;IACxC,MAAMC,aAAa,GAAgB,IAAID,GAAG,EAAE;IAE5ChB,GAAG,CAACE,OAAO,CAACC,SAAS,IAAG;MACtBc,aAAa,CAACC,GAAG,CAACf,SAAS,CAAC5K,UAAU,CAAC;MACvC4K,SAAS,CAACC,OAAO,CAACF,OAAO,CAAEG,KAAU,IAAI;QACvCU,SAAS,CAACG,GAAG,CAACb,KAAK,CAAC5K,MAAM,CAAC;MAC7B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAAC0E,MAAM,GAAGgH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;IACnC,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,IAAI,CAACH,aAAa,CAAC;IAE3C,OAAO;MACL9G,MAAM,EAAEgH,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC;MAC7BJ,UAAU,EAAEQ,KAAK,CAACC,IAAI,CAACH,aAAa;KACrC;EACH;EAEAI,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACtD,WAAW,CAACzJ,gBAAgB,IAAI,IAAI,CAACyJ,WAAW,CAACxJ,cAAc,EAAE;MACxE,MAAMgG,SAAS,GAAG,IAAI8D,IAAI,CAAC,IAAI,CAACN,WAAW,CAACzJ,gBAAgB,CAAC;MAC7D,MAAMmG,OAAO,GAAG,IAAI4D,IAAI,CAAC,IAAI,CAACN,WAAW,CAACxJ,cAAc,CAAC;MACzD,IAAIgG,SAAS,IAAIE,OAAO,IAAIF,SAAS,GAAGE,OAAO,EAAE;QAC/C,IAAI,CAAC2B,OAAO,CAACwC,aAAa,CAAC,CAAC,eAAe,CAAC,CAAC;MAC/C;IACF;EACF;EAEAM,kBAAkBA,CAAA;IAChB,IAAI,CAAC5B,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC+D,cAAc,EAAE;IACrB,IAAI,CAAC/E,aAAa,CAACgF,mCAAmC,CAAC;MACrDvC,IAAI,EAAE;QACJwC,YAAY,EAAE,IAAI,CAACxD,WAAW,CAACC,kBAAkB,CAAC0B,GAAG;QACrDpL,gBAAgB,EAAE,IAAI,CAACyJ,WAAW,CAACzJ,gBAAgB,GAAG,IAAI,CAACiK,UAAU,CAAC,IAAI,CAACR,WAAW,CAACzJ,gBAAgB,CAAC,GAAGiJ,SAAS;QACpHhJ,cAAc,EAAE,IAAI,CAACwJ,WAAW,CAACxJ,cAAc,GAAG,IAAI,CAACgK,UAAU,CAAC,IAAI,CAACR,WAAW,CAACxJ,cAAc,CAAC,GAAGgJ;;KAExG,CAAC,CAACM,SAAS,CAACH,GAAG,IAAG;MACjB,IAAI,CAACJ,OAAO,GAAG,KAAK;MACpB,IAAII,GAAG,CAAC4B,OAAO,IAAI5B,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACtC,IAAI,CAACwC,gBAAgB,GAAG9D,GAAG,CAAC4B,OAAO,GAAG5B,GAAG,CAAC4B,OAAO,GAAG,EAAE;QACtD,IAAI5B,GAAG,CAAC4B,OAAO,EAAE;UACf,IAAI,CAACkC,gBAAgB,GAAG,CAAC,GAAG9D,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACwB,sBAAsB,CAACpD,GAAG,CAAC4B,OAAO,CAAC;UACxC,IAAI,CAACmC,mBAAmB,GAAG,IAAI,CAAC1B,8BAA8B,CAACrC,GAAG,CAAC4B,OAAO,CAAC;UAC3E;UACA,IAAI,CAACoC,mBAAmB,CAAChE,GAAG,CAAC4B,OAAO,CAAC;UACrC;UACA,IAAI,CAACqC,oBAAoB,CAACjE,GAAG,CAAC4B,OAAO,CAAC;QACxC;MACF;IACF,CAAC,CAAC;EACJ;EAEA;EACAoC,mBAAmBA,CAACE,IAA6B;IAC/C,MAAMC,WAAW,GAAG,IAAIC,GAAG,EAAyC;IAEpEF,IAAI,CAAC1B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM4B,SAAS,GAAG5B,SAAS,CAAC5K,UAAU,IAAI,EAAE,CAAC,CAAC;MAE9C4K,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,MAAM2B,YAAY,GAAG3B,KAAK,CAAC7K,aAAa,IAAI,KAAK,CAAC,CAAC;QACnD,MAAM8K,KAAK,GAAGD,KAAK,CAAC5K,MAAM,IAAI,CAAC;QAE/B,IAAI,CAACoM,WAAW,CAACI,GAAG,CAACD,YAAY,CAAC,EAAE;UAClCH,WAAW,CAACK,GAAG,CAACF,YAAY,EAAE,IAAIF,GAAG,EAA4B,CAAC;QACpE;QAEA,MAAMK,QAAQ,GAAGN,WAAW,CAACO,GAAG,CAACJ,YAAY,CAAE;QAE/C,IAAI,CAACG,QAAQ,CAACF,GAAG,CAAC3B,KAAK,CAAC,EAAE;UACxB6B,QAAQ,CAACD,GAAG,CAAC5B,KAAK,EAAE,EAAE,CAAC;QACzB;QAEA6B,QAAQ,CAACC,GAAG,CAAC9B,KAAK,CAAE,CAACC,IAAI,CAAC;UACxBhL,UAAU,EAAEwM,SAAS;UAAE;UACvBvM,aAAa,EAAEwM,YAAY;UAAE;UAC7B3M,QAAQ,EAAEgL,KAAK,CAAChL,QAAQ,IAAI,CAAC;UAC7BI,MAAM,EAAE6K,KAAK;UACbhM,gBAAgB,EAAE+L,KAAK,CAAC/L,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAE8L,KAAK,CAAC9L,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACoI,cAAc,GAAGoE,KAAK,CAACC,IAAI,CAACS,WAAW,CAACxC,OAAO,EAAE,CAAC,CAACG,GAAG,CAAC,CAAC,CAACwC,YAAY,EAAEG,QAAQ,CAAC,KAAI;MACvF,MAAMhI,MAAM,GAAiBgH,KAAK,CAACC,IAAI,CAACe,QAAQ,CAAC9C,OAAO,EAAE,CAAC,CACxDjI,IAAI,CAAC,CAAC,CAACoJ,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC,CAAC;MAAA,CAC1BhB,GAAG,CAAC,CAAC,CAACxF,WAAW,EAAEL,MAAM,CAAC,MAAM;QAC/BK,WAAW;QACXL,MAAM,EAAEA,MAAM,CAACvC,IAAI,CAAC,CAACoJ,CAAC,EAAEC,CAAC,KAAI;UAC3B;UACA,IAAID,CAAC,CAACjL,UAAU,KAAKkL,CAAC,CAAClL,UAAU,EAAE;YACjC,OAAOiL,CAAC,CAACjL,UAAU,CAAC8M,aAAa,CAAC5B,CAAC,CAAClL,UAAU,CAAC;UACjD;UACA,OAAOiL,CAAC,CAAC/K,MAAM,GAAGgL,CAAC,CAAChL,MAAM;QAC5B,CAAC,CAAC;QACFd,QAAQ,EAAE;OACX,CAAC,CAAC;MAEL,OAAO;QACLwG,IAAI,EAAE6G,YAAY;QAClB7H,MAAM;QACNxF,QAAQ,EAAE;OACX;IACH,CAAC,CAAC,CAACyC,IAAI,CAAC,CAACoJ,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrF,IAAI,CAACkH,aAAa,CAAC5B,CAAC,CAACtF,IAAI,CAAC,CAAC;IAE/C;IACA,IAAI,CAAC6B,eAAe,GAAG,IAAI,CAACD,cAAc,CAACyC,GAAG,CAAC8C,EAAE,IAAIA,EAAE,CAACnH,IAAI,CAAC;IAC7D,IAAI,CAACoH,qBAAqB,EAAE;EAC9B;EAEA;EACAA,qBAAqBA,CAAA;IACnB,MAAMxB,SAAS,GAAG,IAAIC,GAAG,EAAU;IAEnC,IAAI,CAACjE,cAAc,CAACmD,OAAO,CAACsC,QAAQ,IAAG;MACrC,IAAI,CAAC,IAAI,CAACvF,gBAAgB,IAAIuF,QAAQ,CAACrH,IAAI,KAAK,IAAI,CAAC8B,gBAAgB,EAAE;QACrEuF,QAAQ,CAACrI,MAAM,CAAC+F,OAAO,CAACI,KAAK,IAAG;UAC9BS,SAAS,CAACG,GAAG,CAACZ,KAAK,CAACtG,WAAW,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,IAAI,CAAChG,eAAe,GAAGmN,KAAK,CAACC,IAAI,CAACL,SAAS,CAAC,CAAC3J,IAAI,CAAC,CAACoJ,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAGD,CAAC,CAAC;EACpE;EAEA;EACAiC,gBAAgBA,CAAA;IACd,IAAI,CAACF,qBAAqB,EAAE;IAC5B,IAAI,CAAC7P,aAAa,CAACwK,cAAc,GAAG,IAAI,CAACD,gBAAgB;EAC3D;EAIA;EACAyF,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAC3F,cAAc,CAAC4F,MAAM,CAACH,QAAQ,IAAG;MAC3C;MACA,IAAI,IAAI,CAACvF,gBAAgB,IAAIuF,QAAQ,CAACrH,IAAI,KAAK,IAAI,CAAC8B,gBAAgB,EAAE;QACpE,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACvK,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMiQ,OAAO,GAAG,IAAI,CAAClQ,aAAa,CAACC,aAAa,CAACkQ,WAAW,EAAE;QAC9D,MAAMC,gBAAgB,GAAGN,QAAQ,CAACrI,MAAM,CAAC4I,IAAI,CAACzC,KAAK,IACjDA,KAAK,CAAC3G,MAAM,CAACoJ,IAAI,CAAC1C,KAAK,IACrBA,KAAK,CAAC9K,UAAU,CAACsN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IAChDvC,KAAK,CAAC7K,aAAa,CAACqN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,CACpD,CACF;QACD,IAAI,CAACE,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACpQ,aAAa,CAACM,YAAY,EAAE;QACnC,MAAMiQ,iBAAiB,GAAGT,QAAQ,CAACrI,MAAM,CAAC4I,IAAI,CAACzC,KAAK,IAClDA,KAAK,CAAC3G,MAAM,CAACoJ,IAAI,CAAC1C,KAAK,IAAI,IAAI,CAAC6C,mBAAmB,CAAC7C,KAAK,CAAC,CAAC,CAC5D;QACD,IAAI,CAAC4C,iBAAiB,EAAE;UACtB,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACvQ,aAAa,CAACS,WAAW,EAAE;QAClC,MAAM6G,WAAW,GAAGmJ,QAAQ,CAAC,IAAI,CAACzQ,aAAa,CAACS,WAAW,CAAC;QAC5D,MAAMiQ,gBAAgB,GAAGZ,QAAQ,CAACrI,MAAM,CAAC4I,IAAI,CAACzC,KAAK,IACjDA,KAAK,CAACtG,WAAW,KAAKA,WAAW,CAClC;QACD,IAAI,CAACoJ,gBAAgB,EAAE;UACrB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC,CAAC5D,GAAG,CAACgD,QAAQ,IAAG;MAChB;MACA,MAAMa,gBAAgB,GAAG;QAAE,GAAGb;MAAQ,CAAE;MACxCa,gBAAgB,CAAClJ,MAAM,GAAGqI,QAAQ,CAACrI,MAAM,CAACwI,MAAM,CAACrC,KAAK,IAAG;QACvD;QACA,IAAI,IAAI,CAAC5N,aAAa,CAACS,WAAW,EAAE;UAClC,MAAM6G,WAAW,GAAGmJ,QAAQ,CAAC,IAAI,CAACzQ,aAAa,CAACS,WAAW,CAAC;UAC5D,IAAImN,KAAK,CAACtG,WAAW,KAAKA,WAAW,EAAE;YACrC,OAAO,KAAK;UACd;QACF;QAEA;QACA,MAAMsJ,cAAc,GAAGhD,KAAK,CAAC3G,MAAM,CAACoJ,IAAI,CAAC1C,KAAK,IAAG;UAC/C;UACA,IAAI,IAAI,CAAC3N,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMiQ,OAAO,GAAG,IAAI,CAAClQ,aAAa,CAACC,aAAa,CAACkQ,WAAW,EAAE;YAC9D,IAAI,CAACxC,KAAK,CAAC9K,UAAU,CAACsN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACvC,KAAK,CAAC7K,aAAa,CAACqN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAClQ,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACkQ,mBAAmB,CAAC7C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOiD,cAAc;MACvB,CAAC,CAAC,CAAC9D,GAAG,CAACc,KAAK,IAAG;QACb;QACA,MAAMiD,aAAa,GAAG;UAAE,GAAGjD;QAAK,CAAE;QAClCiD,aAAa,CAAC5J,MAAM,GAAG2G,KAAK,CAAC3G,MAAM,CAACgJ,MAAM,CAACtC,KAAK,IAAG;UACjD;UACA,IAAI,IAAI,CAAC3N,aAAa,CAACC,aAAa,EAAE;YACpC,MAAMiQ,OAAO,GAAG,IAAI,CAAClQ,aAAa,CAACC,aAAa,CAACkQ,WAAW,EAAE;YAC9D,IAAI,CAACxC,KAAK,CAAC9K,UAAU,CAACsN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACvC,KAAK,CAAC7K,aAAa,CAACqN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;cACtD,OAAO,KAAK;YACd;UACF;UAEA;UACA,IAAI,IAAI,CAAClQ,aAAa,CAACM,YAAY,EAAE;YACnC,IAAI,CAAC,IAAI,CAACkQ,mBAAmB,CAAC7C,KAAK,CAAC,EAAE;cACpC,OAAO,KAAK;YACd;UACF;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEF,OAAOkD,aAAa;MACtB,CAAC,CAAC;MAEF,OAAOF,gBAAgB;IACzB,CAAC,CAAC;EACJ;EAEA;EACQH,mBAAmBA,CAAC7C,KAAqB;IAC/C,MAAMmD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACpD,KAAK,CAAC;IAEzC,QAAQ,IAAI,CAAC3N,aAAa,CAACM,YAAY;MACrC,KAAK,QAAQ;QACX,OAAOwQ,MAAM,KAAK,QAAQ;MAC5B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,SAAS;MAC7B,KAAK,UAAU;QACb,OAAOA,MAAM,KAAK,UAAU;MAC9B;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACQC,cAAcA,CAACpD,KAAqB;IAC1C,IAAI,CAACA,KAAK,CAAChL,QAAQ,EAAE;MACnB,OAAO,UAAU;IACnB;IAEA,IAAIgL,KAAK,CAAC/L,gBAAgB,IAAI+L,KAAK,CAAC9L,cAAc,EAAE;MAClD,MAAMmP,GAAG,GAAG,IAAIrF,IAAI,EAAE;MACtB,MAAM9D,SAAS,GAAG,IAAI8D,IAAI,CAACgC,KAAK,CAAC/L,gBAAgB,CAAC;MAClD,MAAMmG,OAAO,GAAG,IAAI4D,IAAI,CAACgC,KAAK,CAAC9L,cAAc,CAAC;MAE9C,IAAImP,GAAG,GAAGnJ,SAAS,EAAE;QACnB,OAAO,SAAS;MAClB,CAAC,MAAM,IAAImJ,GAAG,IAAInJ,SAAS,IAAImJ,GAAG,IAAIjJ,OAAO,EAAE;QAC7C,OAAO,QAAQ;MACjB,CAAC,MAAM;QACL,OAAO,SAAS;MAClB;IACF;IAEA,OAAO,SAAS;EAClB;EAEAO,OAAOA,CAACmD,GAAQ;IACdA,GAAG,CAACgB,KAAK,EAAE;EACb;EAEAT,UAAUA,CAAA;IACR,IAAI,CAACrC,KAAK,CAACsH,KAAK,EAAE;IAClB,IAAI,CAACtH,KAAK,CAACuH,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACpI,uBAAuB,CAAClH,gBAAgB,CAAC;IAC9E,IAAI,CAAC+H,KAAK,CAACuH,QAAQ,CAAC,UAAU,EAAE,IAAI,CAACpI,uBAAuB,CAACjH,cAAc,CAAC;IAC5E,IAAI,CAAC8H,KAAK,CAACwH,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACrI,uBAAuB,CAAClH,gBAAgB,EAAE,IAAI,CAACkH,uBAAuB,CAACjH,cAAc,CAAC;EACtI;EAEAuP,gBAAgBA,CAAA;IACd,IAAI,CAACtH,MAAM,CAACuH,QAAQ,CAAC,CAAC,8BAA8B,IAAI,CAAChG,WAAW,EAAEC,kBAAkB,EAAE0B,GAAG,EAAE,CAAC,CAAC;EACnG;EAEA;EACA3I,gBAAgBA,CAACyL,QAAwB;IACvC,IAAI,CAACtI,wBAAwB,GAAGsI,QAAQ,IAAI,IAAI;IAChD,IAAI,CAAClI,aAAa,GAAG;MACnBC,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE,IAAI;MACbE,UAAU,EAAE,CAAC6H,QAAQ;MACrBrF,iBAAiB,EAAEqF,QAAQ,GAAG,CAACA,QAAQ,CAACrH,IAAI,CAAC,GAAG,EAAE;MAClDiC,cAAc,EAAE,EAAE;MAClBpL,cAAc,EAAE;KACjB;IAED;IACA,IAAIwQ,QAAQ,EAAE;MACZA,QAAQ,CAACrI,MAAM,CAAC+F,OAAO,CAACI,KAAK,IAAG;QAC9BA,KAAK,CAAC3L,QAAQ,GAAG,KAAK;QACtB2L,KAAK,CAAC3G,MAAM,CAACuG,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAC1L,QAAQ,GAAG,KAAK,CAAC;MACvD,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACwH,aAAa,CAACmC,IAAI,CAAC,IAAI,CAAC0F,kBAAkB,CAAC;EAClD;EAEA;EACAlK,sBAAsBA,CAACwG,KAAiB;IACtC,IAAIA,KAAK,CAAC3L,QAAQ,EAAE;MAClB2L,KAAK,CAAC3G,MAAM,CAACuG,OAAO,CAACG,KAAK,IAAG;QAC3B,IAAIA,KAAK,CAAChL,QAAQ,EAAE;UAClBgL,KAAK,CAAC1L,QAAQ,GAAG,IAAI;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL2L,KAAK,CAAC3G,MAAM,CAACuG,OAAO,CAACG,KAAK,IAAIA,KAAK,CAAC1L,QAAQ,GAAG,KAAK,CAAC;IACvD;EACF;EAEA;EACAuG,aAAaA,CAACiD,GAAQ;IACpB;IACA,IAAI,CAAC9B,KAAK,CAACsH,KAAK,EAAE;IAClB,IAAI,CAACtH,KAAK,CAACuH,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtJ,aAAa,CAACC,SAAS,CAAC;IAC3D,IAAI,CAAC8B,KAAK,CAACuH,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAACtJ,aAAa,CAACG,OAAO,CAAC;IACzD,IAAI,CAAC4B,KAAK,CAACwH,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACvJ,aAAa,CAACC,SAAS,EAAE,IAAI,CAACD,aAAa,CAACG,OAAO,CAAC;IAElG,IAAI,IAAI,CAAC4B,KAAK,CAACsC,aAAa,CAAC1M,MAAM,GAAG,CAAC,EAAE;MACvC,IAAI,CAACmK,OAAO,CAACwC,aAAa,CAAC,IAAI,CAACvC,KAAK,CAACsC,aAAa,CAAC;MACpD;IACF;IAEA;IACA,MAAMsF,cAAc,GAAU,EAAE;IAEhC,IAAI,IAAI,CAAC3J,aAAa,CAACK,UAAU,EAAE;MACjC;MACA,IAAI,CAACoC,cAAc,CAACmD,OAAO,CAACsC,QAAQ,IAAG;QACrCA,QAAQ,CAACrI,MAAM,CAAC+F,OAAO,CAACI,KAAK,IAAG;UAC9BA,KAAK,CAAC3G,MAAM,CAACuG,OAAO,CAACG,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAChL,QAAQ,EAAE;cAClB4O,cAAc,CAAC1D,IAAI,CAAC;gBAClBlL,QAAQ,EAAEgL,KAAK,CAAChL,QAAQ;gBACxBf,gBAAgB,EAAE,IAAI,CAACiK,UAAU,CAAC,IAAI,CAACjE,aAAa,CAACC,SAAS,CAAC;gBAC/DhG,cAAc,EAAE,IAAI,CAACgK,UAAU,CAAC,IAAI,CAACjE,aAAa,CAACG,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACP,wBAAwB,EAAE;QACjC,IAAI,CAACA,wBAAwB,CAACC,MAAM,CAAC+F,OAAO,CAACI,KAAK,IAAG;UACnDA,KAAK,CAAC3G,MAAM,CAACuG,OAAO,CAACG,KAAK,IAAG;YAC3B,IAAIA,KAAK,CAAC1L,QAAQ,IAAI0L,KAAK,CAAChL,QAAQ,EAAE;cACpC4O,cAAc,CAAC1D,IAAI,CAAC;gBAClBlL,QAAQ,EAAEgL,KAAK,CAAChL,QAAQ;gBACxBf,gBAAgB,EAAE,IAAI,CAACiK,UAAU,CAAC,IAAI,CAACjE,aAAa,CAACC,SAAS,CAAC;gBAC/DhG,cAAc,EAAE,IAAI,CAACgK,UAAU,CAAC,IAAI,CAACjE,aAAa,CAACG,OAAO;eAC3D,CAAC;YACJ;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIwJ,cAAc,CAAChS,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAACmK,OAAO,CAACwC,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC;MACzC;IACF;IAEA;IACA,IAAI,CAACtC,aAAa,CAACwC,oCAAoC,CAAC;MACtDC,IAAI,EAAEkF;KACP,CAAC,CAACpG,SAAS,CAACH,GAAG,IAAG;MACjB,IAAIA,GAAG,CAACsB,UAAU,IAAI,CAAC,EAAE;QACvB,IAAI,CAAC5C,OAAO,CAAC6C,aAAa,CAAC,QAAQgF,cAAc,CAAChS,MAAM,WAAW,CAAC;QACpE,IAAI,CAACiN,kBAAkB,EAAE;QACzBf,GAAG,CAACgB,KAAK,EAAE;MACb;IACF,CAAC,CAAC;EACJ;EAEA;EACAxJ,cAAcA,CAAC0K,KAAqB;IAClC,MAAMmD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACpD,KAAK,CAAC;IACzC,OAAO,UAAUmD,MAAM,EAAE;EAC3B;EAEA;EACAU,eAAeA,CAAC7D,KAAqB;IACnC,IAAIA,KAAK,CAAChL,QAAQ,EAAE;MAClB;MACA,IAAI,CAACD,SAAS,CAAC,IAAI,CAAC+O,MAAM,EAAE9D,KAAK,CAAC;IACpC;EACF;EAEA;EACAsB,oBAAoBA,CAACC,IAA6B;IAChD,IAAI,CAACvE,eAAe,GAAG,EAAE;IAEzBuE,IAAI,CAAC1B,OAAO,CAACC,SAAS,IAAG;MACvB,MAAM4B,SAAS,GAAG5B,SAAS,CAAC5K,UAAU,IAAI,EAAE;MAE5C4K,SAAS,CAACC,OAAO,EAAEF,OAAO,CAACG,KAAK,IAAG;QACjC,IAAI,CAAChD,eAAe,CAACkD,IAAI,CAAC;UACxBhL,UAAU,EAAEwM,SAAS;UACrBvM,aAAa,EAAE6K,KAAK,CAAC7K,aAAa,IAAI,KAAK;UAC3CH,QAAQ,EAAEgL,KAAK,CAAChL,QAAQ,IAAI,CAAC;UAC7BI,MAAM,EAAE4K,KAAK,CAAC5K,MAAM,IAAI,CAAC;UACzBnB,gBAAgB,EAAE+L,KAAK,CAAC/L,gBAAgB,IAAI,EAAE;UAC9CC,cAAc,EAAE8L,KAAK,CAAC9L,cAAc,IAAI,EAAE;UAC1CI,QAAQ,EAAE;SACX,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAAC7B,QAAQ,EAAE;EACjB;EAEA;EACAe,WAAWA,CAACuQ,IAAsB;IAChC,IAAI,CAACjQ,QAAQ,GAAGiQ,IAAI;EACtB;EAEA;EACAtR,QAAQA,CAAA;IACN,IAAI,CAACmB,cAAc,GAAG,IAAI,CAACoJ,eAAe,CAACsF,MAAM,CAACtC,KAAK,IAAG;MACxD;MACA,IAAI,IAAI,CAAC3N,aAAa,CAACC,aAAa,EAAE;QACpC,MAAMiQ,OAAO,GAAG,IAAI,CAAClQ,aAAa,CAACC,aAAa,CAACkQ,WAAW,EAAE;QAC9D,IAAI,CAACxC,KAAK,CAAC9K,UAAU,CAACsN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,IACnD,CAACvC,KAAK,CAAC7K,aAAa,CAACqN,WAAW,EAAE,CAACG,QAAQ,CAACJ,OAAO,CAAC,EAAE;UACtD,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC3F,gBAAgB,IAAIoD,KAAK,CAAC7K,aAAa,KAAK,IAAI,CAACyH,gBAAgB,EAAE;QAC1E,OAAO,KAAK;MACd;MAEA;MACA,IAAI,IAAI,CAACvK,aAAa,CAACM,YAAY,EAAE;QACnC,IAAI,CAAC,IAAI,CAACkQ,mBAAmB,CAAC7C,KAAK,CAAC,EAAE;UACpC,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAAC3N,aAAa,CAACS,WAAW,EAAE;QAClC,MAAM6G,WAAW,GAAGmJ,QAAQ,CAAC,IAAI,CAACzQ,aAAa,CAACS,WAAW,CAAC;QAC5D,IAAIkN,KAAK,CAAC5K,MAAM,KAAKuE,WAAW,EAAE;UAChC,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC,CAAC;IAEF;IACA,IAAI,CAAC/D,WAAW,GAAG,CAAC;IACpB,IAAI,CAACoO,gBAAgB,EAAE;EACzB;EAEA;EACAA,gBAAgBA,CAAA;IACd,IAAI,CAAC7N,UAAU,GAAGoB,IAAI,CAAC0M,IAAI,CAAC,IAAI,CAACrQ,cAAc,CAAChC,MAAM,GAAG,IAAI,CAACuB,QAAQ,CAAC;IACvE,MAAM+Q,UAAU,GAAG,CAAC,IAAI,CAACtO,WAAW,GAAG,CAAC,IAAI,IAAI,CAACzC,QAAQ;IACzD,MAAMgR,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC/Q,QAAQ;IAC3C,IAAI,CAACwE,eAAe,GAAG,IAAI,CAAC/D,cAAc,CAACwQ,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EACxE;EAEA;EACA9Q,gBAAgBA,CAAA;IACd,IAAI,CAACuC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACoO,gBAAgB,EAAE;EACzB;EAEA;EACArO,QAAQA,CAAC0O,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,IAAI,CAAClO,UAAU,EAAE;MACxC,IAAI,CAACP,WAAW,GAAGyO,IAAI;MACvB,IAAI,CAACL,gBAAgB,EAAE;IACzB;EACF;EAEA;EACA5N,eAAeA,CAAA;IACb,MAAMkO,KAAK,GAAa,EAAE;IAC1B,MAAMC,UAAU,GAAG,CAAC;IACpB,IAAIC,KAAK,GAAGjN,IAAI,CAACkN,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC7O,WAAW,GAAG2B,IAAI,CAAC0I,KAAK,CAACsE,UAAU,GAAG,CAAC,CAAC,CAAC;IACtE,IAAIG,GAAG,GAAGnN,IAAI,CAACC,GAAG,CAAC,IAAI,CAACrB,UAAU,EAAEqO,KAAK,GAAGD,UAAU,GAAG,CAAC,CAAC;IAE3D,IAAIG,GAAG,GAAGF,KAAK,GAAG,CAAC,GAAGD,UAAU,EAAE;MAChCC,KAAK,GAAGjN,IAAI,CAACkN,GAAG,CAAC,CAAC,EAAEC,GAAG,GAAGH,UAAU,GAAG,CAAC,CAAC;IAC3C;IAEA,KAAK,IAAII,CAAC,GAAGH,KAAK,EAAEG,CAAC,IAAID,GAAG,EAAEC,CAAC,EAAE,EAAE;MACjCL,KAAK,CAACpE,IAAI,CAACyE,CAAC,CAAC;IACf;IAEA,OAAOL,KAAK;EACd;EAEA;EACA9N,iBAAiBA,CAAA;IACf,IAAI,CAACmB,eAAe,CAACkI,OAAO,CAACG,KAAK,IAAG;MACnC,IAAIA,KAAK,CAAChL,QAAQ,EAAE;QAClBgL,KAAK,CAAC1L,QAAQ,GAAG,IAAI,CAACiC,SAAS;MACjC;IACF,CAAC,CAAC;IACF,IAAI,CAACqO,oBAAoB,EAAE;EAC7B;EAEA;EACArQ,sBAAsBA,CAAA;IACpB,IAAI,CAACqQ,oBAAoB,EAAE;IAC3B,IAAI,CAACrO,SAAS,GAAG,IAAI,CAACoB,eAAe,CAACkN,KAAK,CAAC7E,KAAK,IAC/C,CAACA,KAAK,CAAChL,QAAQ,IAAIgL,KAAK,CAAC1L,QAAQ,CAClC;EACH;EAEA;EACAsQ,oBAAoBA,CAAA;IAClB,IAAI,CAACjT,cAAc,GAAG,IAAI,CAACqL,eAAe,CAACsF,MAAM,CAACtC,KAAK,IAAIA,KAAK,CAAC1L,QAAQ,CAAC;EAC5E;EAEA;EACAyC,IAAIA,CAAC+N,KAAa;IAChB,IAAI,IAAI,CAACrN,SAAS,KAAKqN,KAAK,EAAE;MAC5B,IAAI,CAACpN,aAAa,GAAG,IAAI,CAACA,aAAa,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK;IACpE,CAAC,MAAM;MACL,IAAI,CAACD,SAAS,GAAGqN,KAAK;MACtB,IAAI,CAACpN,aAAa,GAAG,KAAK;IAC5B;IAEA,IAAI,CAAC9D,cAAc,CAACmD,IAAI,CAAC,CAACoJ,CAAC,EAAEC,CAAC,KAAI;MAChC,IAAI2E,MAAM,GAAI5E,CAAS,CAAC2E,KAAK,CAAC;MAC9B,IAAIE,MAAM,GAAI5E,CAAS,CAAC0E,KAAK,CAAC;MAE9B;MACA,IAAIA,KAAK,CAACnC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC1BoC,MAAM,GAAGA,MAAM,GAAG,IAAI/G,IAAI,CAAC+G,MAAM,CAAC,CAACE,OAAO,EAAE,GAAG,CAAC;QAChDD,MAAM,GAAGA,MAAM,GAAG,IAAIhH,IAAI,CAACgH,MAAM,CAAC,CAACC,OAAO,EAAE,GAAG,CAAC;MAClD;MAEA;MACA,IAAIH,KAAK,KAAK,QAAQ,EAAE;QACtBC,MAAM,GAAGG,MAAM,CAACH,MAAM,CAAC,IAAI,CAAC;QAC5BC,MAAM,GAAGE,MAAM,CAACF,MAAM,CAAC,IAAI,CAAC;MAC9B;MAEA;MACA,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;QAC9BA,MAAM,GAAGA,MAAM,CAACvC,WAAW,EAAE;QAC7BwC,MAAM,GAAGA,MAAM,CAACxC,WAAW,EAAE;MAC/B;MAEA,IAAIuC,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACtN,aAAa,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC9C;MACA,IAAIqN,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO,IAAI,CAACtN,aAAa,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9C;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAACsM,gBAAgB,EAAE;EACzB;EAEA;EACApM,cAAcA,CAACuN,MAAc,EAAEnF,KAAqB;IAClD,OAAOA,KAAK,CAAChL,QAAQ;EACvB;EAEA;EACAO,aAAaA,CAACyK,KAAqB;IACjC,MAAMmD,MAAM,GAAG,IAAI,CAACC,cAAc,CAACpD,KAAK,CAAC;IAEzC,QAAQmD,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,SAAS;QACZ,OAAO,KAAK;MACd,KAAK,UAAU;QACb,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF;EAEA;EACAvM,UAAUA,CAAA;IACR;IACA,MAAMwO,UAAU,GAAG,IAAI,CAACC,WAAW,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEI,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC;IACrCG,IAAI,CAACM,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BH,IAAI,CAACM,YAAY,CAAC,UAAU,EAAE,UAAU,IAAI/H,IAAI,EAAE,CAACgI,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACrFR,IAAI,CAACS,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChCT,QAAQ,CAAChH,IAAI,CAAC0H,WAAW,CAACX,IAAI,CAAC;IAC/BA,IAAI,CAACY,KAAK,EAAE;IACZX,QAAQ,CAAChH,IAAI,CAAC4H,WAAW,CAACb,IAAI,CAAC;EACjC;EAEA;EACQJ,WAAWA,CAAA;IACjB,MAAMkB,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC;IACxD,MAAMC,IAAI,GAAG,IAAI,CAAC5S,cAAc,CAACuL,GAAG,CAACa,KAAK,IAAI,CAC5CA,KAAK,CAAC9K,UAAU,EAChB8K,KAAK,CAAC7K,aAAa,EACnB,GAAG6K,KAAK,CAAC5K,MAAM,GAAG,EAClB4K,KAAK,CAAC/L,gBAAgB,IAAI,KAAK,EAC/B+L,KAAK,CAAC9L,cAAc,IAAI,KAAK,EAC7B,IAAI,CAACqB,aAAa,CAACyK,KAAK,CAAC,CAC1B,CAAC;IAEF,MAAMoF,UAAU,GAAG,CAACmB,OAAO,EAAE,GAAGC,IAAI,CAAC,CAClCrH,GAAG,CAACsH,GAAG,IAAIA,GAAG,CAACtH,GAAG,CAACuH,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAClDA,IAAI,CAAC,IAAI,CAAC;IAEb,OAAO,QAAQ,GAAGvB,UAAU,CAAC,CAAC;EAChC;;;uCAj2BWzJ,0BAA0B,EAAA5K,EAAA,CAAA6V,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/V,EAAA,CAAA6V,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAjW,EAAA,CAAA6V,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAnW,EAAA,CAAA6V,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAArW,EAAA,CAAA6V,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAvW,EAAA,CAAA6V,iBAAA,CAAAS,EAAA,CAAAE,gBAAA,GAAAxW,EAAA,CAAA6V,iBAAA,CAAAY,EAAA,CAAAC,MAAA,GAAA1W,EAAA,CAAA6V,iBAAA,CAAAc,EAAA,CAAAC,YAAA;IAAA;EAAA;;;YAA1BhM,0BAA0B;MAAAiM,SAAA;MAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;uCAR1B,EAAE,GAAAhX,EAAA,CAAAkX,0BAAA,EAAAlX,EAAA,CAAAmX,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpEbhX,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAc,SAAA,qBAAiC;UACnCd,EAAA,CAAAG,YAAA,EAAiB;UAEfH,EADF,CAAAC,cAAA,mBAAc,aACyB;UAAAD,EAAA,CAAAE,MAAA,gRAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAO/EH,EAJR,CAAAC,cAAA,cAA2B,cACK,cACN,cACqC,iBACT;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,qBAC0C;UADdD,EAAA,CAAAe,gBAAA,2BAAA0W,wEAAAxW,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA1X,EAAA,CAAAqB,kBAAA,CAAA4V,GAAA,CAAAtK,WAAA,CAAAC,kBAAA,EAAA3L,MAAA,MAAAgW,GAAA,CAAAtK,WAAA,CAAAC,kBAAA,GAAA3L,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAA4C;UACtEjB,EAAA,CAAAyB,UAAA,4BAAAkW,yEAAA;YAAA3X,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA,OAAA1X,EAAA,CAAAwB,WAAA,CAAkByV,GAAA,CAAAnJ,kBAAA,EAAoB;UAAA,EAAC;UACvC9N,EAAA,CAAAiC,UAAA,KAAA2V,gDAAA,wBAAoE;UAK1E5X,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACqC,iBACP;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAC,cAAA,qBACwC;UADTD,EAAA,CAAAe,gBAAA,2BAAA8W,wEAAA5W,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA1X,EAAA,CAAAqB,kBAAA,CAAA4V,GAAA,CAAApL,gBAAA,EAAA5K,MAAA,MAAAgW,GAAA,CAAApL,gBAAA,GAAA5K,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAA8B;UAC3DjB,EAAA,CAAAyB,UAAA,4BAAAqW,yEAAA;YAAA9X,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA,OAAA1X,EAAA,CAAAwB,WAAA,CAAkByV,GAAA,CAAA5F,gBAAA,EAAkB;UAAA,EAAC;UACrCrR,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,gCAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAiC,UAAA,KAAA8V,gDAAA,wBAAuE;UAK7E/X,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAsB,eAC8B,iBACJ;UAAAD,EAAA,CAAAE,MAAA,iCAC5C;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,yBAA4B;UAC1BD,EAAA,CAAAc,SAAA,mBAAoD;UACpDd,EAAA,CAAAC,cAAA,iBAC6C;UAA3CD,EAAA,CAAAe,gBAAA,2BAAAiX,oEAAA/W,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA1X,EAAA,CAAAqB,kBAAA,CAAA4V,GAAA,CAAAtK,WAAA,CAAAzJ,gBAAA,EAAAjC,MAAA,MAAAgW,GAAA,CAAAtK,WAAA,CAAAzJ,gBAAA,GAAAjC,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAA0C;UAD5CjB,EAAA,CAAAG,YAAA,EAC6C;UAC7CH,EAAA,CAAAc,SAAA,4BAA8D;UAChEd,EAAA,CAAAG,YAAA,EAAgB;UAChBH,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,qBAAe;UACbD,EAAA,CAAAc,SAAA,mBAAoD;UACpDd,EAAA,CAAAC,cAAA,iBAC2C;UAAzCD,EAAA,CAAAe,gBAAA,2BAAAkX,oEAAAhX,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA1X,EAAA,CAAAqB,kBAAA,CAAA4V,GAAA,CAAAtK,WAAA,CAAAxJ,cAAA,EAAAlC,MAAA,MAAAgW,GAAA,CAAAtK,WAAA,CAAAxJ,cAAA,GAAAlC,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAAwC;UAD1CjB,EAAA,CAAAG,YAAA,EAC2C;UAC3CH,EAAA,CAAAc,SAAA,4BAA4D;UAGlEd,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACF;UAGFH,EAFJ,CAAAC,cAAA,eAAuC,eACoB,iBACV;UAC3CD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAENH,EADF,CAAAC,cAAA,eAA6B,iBAEF;UAAvBD,EAAA,CAAAe,gBAAA,2BAAAmX,oEAAAjX,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA1X,EAAA,CAAAqB,kBAAA,CAAA4V,GAAA,CAAAvL,QAAA,EAAAzK,MAAA,MAAAgW,GAAA,CAAAvL,QAAA,GAAAzK,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAAsB;UADxBjB,EAAA,CAAAG,YAAA,EACyB;UACzBH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UACFF,EADE,CAAAG,YAAA,EAAQ,EACJ;UAEJH,EADF,CAAAC,cAAA,eAA6B,iBAEc;UAAvBD,EAAA,CAAAe,gBAAA,2BAAAoX,oEAAAlX,MAAA;YAAAjB,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA1X,EAAA,CAAAqB,kBAAA,CAAA4V,GAAA,CAAAvL,QAAA,EAAAzK,MAAA,MAAAgW,GAAA,CAAAvL,QAAA,GAAAzK,MAAA;YAAA,OAAAjB,EAAA,CAAAwB,WAAA,CAAAP,MAAA;UAAA,EAAsB;UADxCjB,EAAA,CAAAG,YAAA,EACyC;UACzCH,EAAA,CAAAC,cAAA,iBAAwD;UACtDD,EAAA,CAAAE,MAAA,oDACF;UAGNF,EAHM,CAAAG,YAAA,EAAQ,EACJ,EACF,EACF;UAIFH,EAFJ,CAAAC,cAAA,eAAwC,eACS,kBACgC;UAA/BD,EAAA,CAAAyB,UAAA,mBAAA2W,6DAAA;YAAApY,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA,OAAA1X,EAAA,CAAAwB,WAAA,CAASyV,GAAA,CAAAnJ,kBAAA,EAAoB;UAAA,EAAC;UAC1E9N,EAAA,CAAAE,MAAA,sBAAG;UAAAF,EAAA,CAAAc,SAAA,aAA6B;UAClCd,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAiE;UAA7BD,EAAA,CAAAyB,UAAA,mBAAA4W,6DAAA;YAAArY,EAAA,CAAAkB,aAAA,CAAAwW,GAAA;YAAA,OAAA1X,EAAA,CAAAwB,WAAA,CAASyV,GAAA,CAAAtR,gBAAA,EAAkB;UAAA,EAAC;UAC9D3F,EAAA,CAAAE,MAAA,8CACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;UA+QNH,EA5QA,CAAAiC,UAAA,KAAAqW,0CAAA,oBAAqE,KAAAC,0CAAA,oBA2DmB,KAAAC,0CAAA,kBA6HF,KAAAC,0CAAA,kBAwEc,KAAAC,0CAAA,kBAYtD;UAalD1Y,EADE,CAAAG,YAAA,EAAe,EACP;UAwEVH,EArEA,CAAAiC,UAAA,KAAA0W,kDAAA,iCAAA3Y,EAAA,CAAA4Y,sBAAA,CAAgE,KAAAC,kDAAA,gCAAA7Y,EAAA,CAAA4Y,sBAAA,CAwDG,KAAAE,kDAAA,iCAAA9Y,EAAA,CAAA4Y,sBAAA,CAaf;;;;;UA5aZ5Y,EAAA,CAAAM,SAAA,IAA4C;UAA5CN,EAAA,CAAA2C,gBAAA,YAAAsU,GAAA,CAAAtK,WAAA,CAAAC,kBAAA,CAA4C;UAE1C5M,EAAA,CAAAM,SAAA,EAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA6W,GAAA,CAAA9I,oBAAA,CAAuB;UAStBnO,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAA2C,gBAAA,YAAAsU,GAAA,CAAApL,gBAAA,CAA8B;UAG3B7L,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,YAAA6W,GAAA,CAAArL,eAAA,CAAkB;UAYY5L,EAAA,CAAAM,SAAA,GAA0B;UAA1BN,EAAA,CAAAI,UAAA,iBAAA2Y,aAAA,CAA0B;UACtF/Y,EAAA,CAAA2C,gBAAA,YAAAsU,GAAA,CAAAtK,WAAA,CAAAzJ,gBAAA,CAA0C;UAMgBlD,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,iBAAA4Y,WAAA,CAAwB;UAClFhZ,EAAA,CAAA2C,gBAAA,YAAAsU,GAAA,CAAAtK,WAAA,CAAAxJ,cAAA,CAAwC;UAWkDnD,EAAA,CAAAM,SAAA,GAAc;UAAdN,EAAA,CAAAI,UAAA,eAAc;UACxGJ,EAAA,CAAA2C,gBAAA,YAAAsU,GAAA,CAAAvL,QAAA,CAAsB;UAOtB1L,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,gBAAe;UAACJ,EAAA,CAAA2C,gBAAA,YAAAsU,GAAA,CAAAvL,QAAA,CAAsB;UAsBf1L,EAAA,CAAAM,SAAA,IAAgC;UAAhCN,EAAA,CAAAI,UAAA,SAAA6W,GAAA,CAAAhL,eAAA,CAAApL,MAAA,KAAgC;UA2DrCb,EAAA,CAAAM,SAAA,EAAwD;UAAxDN,EAAA,CAAAI,UAAA,SAAA6W,GAAA,CAAAlU,QAAA,gBAAAkU,GAAA,CAAAhL,eAAA,CAAApL,MAAA,KAAwD;UA6HzDb,EAAA,CAAAM,SAAA,EAAuD;UAAvDN,EAAA,CAAAI,UAAA,SAAA6W,GAAA,CAAAlU,QAAA,eAAAkU,GAAA,CAAAhL,eAAA,CAAApL,MAAA,KAAuD;UAwErDb,EAAA,CAAAM,SAAA,EAAmE;UAAnEN,EAAA,CAAAI,UAAA,SAAA6W,GAAA,CAAAhL,eAAA,CAAApL,MAAA,UAAAoW,GAAA,CAAA7G,gBAAA,CAAAvP,MAAA,OAAmE;UAYnEb,EAAA,CAAAM,SAAA,EAAa;UAAbN,EAAA,CAAAI,UAAA,SAAA6W,GAAA,CAAA/K,OAAA,CAAa;;;qBD7R5C3M,YAAY,EAAA0Z,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,QAAA,EAAEvZ,YAAY,EAAAwZ,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,yBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,OAAA,EAAAzD,EAAA,CAAA0D,eAAA,EAAA1D,EAAA,CAAA2D,mBAAA,EAAA3D,EAAA,CAAA4D,qBAAA,EAAA5D,EAAA,CAAA6D,qBAAA,EAAA7D,EAAA,CAAA8D,mBAAA,EAAA9D,EAAA,CAAA+D,gBAAA,EAAA/D,EAAA,CAAAgE,iBAAA,EAAAhE,EAAA,CAAAiE,iBAAA,EAAAjE,EAAA,CAAAkE,oBAAA,EAAAlE,EAAA,CAAAmE,iBAAA,EAAAnE,EAAA,CAAAoE,eAAA,EAAApE,EAAA,CAAAqE,qBAAA,EAAArE,EAAA,CAAAsE,qBAAA,EAAAC,GAAA,CAAAC,mBAAA,EAAE7a,iBAAiB,EAC7CF,kBAAkB,EAAEC,mBAAmB;MAAA+a,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}