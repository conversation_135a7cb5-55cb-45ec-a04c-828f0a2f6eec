{"ast": null, "code": "import { map } from 'rxjs/operators';\nimport { BaseService } from '../base-service';\nimport { apiFileGetFileGet } from '../fn/file/api-file-get-file-get';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../api-configuration\";\nimport * as i2 from \"@angular/common/http\";\nexport let FileService = /*#__PURE__*/(() => {\n  class FileService extends BaseService {\n    constructor(config, http) {\n      super(config, http);\n    }\n    /** Path part for operation `apiFileGetFileGet()` */\n    static {\n      this.ApiFileGetFileGetPath = '/api/File/GetFile';\n    }\n    /**\n     * This method provides access to the full `HttpResponse`, allowing access to response headers.\n     * To access only the response body, use `apiFileGetFileGet()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiFileGetFileGet$Response(params, context) {\n      return apiFileGetFileGet(this.http, this.rootUrl, params, context);\n    }\n    /**\n     * This method provides access only to the response body.\n     * To access the full response (for headers, for example), `apiFileGetFileGet$Response()` instead.\n     *\n     * This method doesn't expect any request body.\n     */\n    apiFileGetFileGet(params, context) {\n      return this.apiFileGetFileGet$Response(params, context).pipe(map(r => r.body));\n    }\n    /**\n     * 自定義方法：取得檔案並返回 Blob\n     * 這個方法正確處理檔案下載，返回 Observable<Blob>\n     *\n     * @param relativePath 相對路徑\n     * @param fileName 檔案名稱\n     * @param context HTTP 上下文\n     * @returns Observable<Blob>\n     */\n    getFile(relativePath, fileName, context) {\n      // 直接使用 HttpClient 發送請求，設置正確的 responseType\n      const url = `${this.rootUrl}${FileService.ApiFileGetFileGetPath}`;\n      const httpParams = new URLSearchParams();\n      if (relativePath) {\n        httpParams.set('relativePath', relativePath);\n      }\n      if (fileName) {\n        httpParams.set('fileName', fileName);\n      }\n      return this.http.get(`${url}?${httpParams.toString()}`, {\n        responseType: 'blob',\n        context\n      });\n    }\n    static {\n      this.ɵfac = function FileService_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || FileService)(i0.ɵɵinject(i1.ApiConfiguration), i0.ɵɵinject(i2.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: FileService,\n        factory: FileService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return FileService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}